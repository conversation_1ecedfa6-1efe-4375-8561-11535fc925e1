﻿namespace BlueGuava.Reporting.Repository.S3
{
    public interface IS3Repository
    {
        Task<T?> RetrieveAsync<T>(string key, CancellationToken token = default) where T : class;
        Task<Stream?> RetrieveAsync(string key, CancellationToken token = default);
        System.Threading.Tasks.Task PutFileAsync(byte[] payload, string key, string folderSuffix, Dictionary<string, string>? tags = null);
        System.Threading.Tasks.Task PutFileAsync(string payload, string bucketName, string fileKey, string typeTag, Dictionary<string, string>? tags = null);
        Task<bool> ExistsAsync(string fileKey);
        Task<string> SearchAsync(string bucketPrefix, string partialFileKey);
        string GetCurrentRegion();
    }
}
