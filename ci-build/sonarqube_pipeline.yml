variables:
  project: "code/BlueGuava.Reporting.Api/BlueGuava.Reporting.Api.csproj"
  nuget_config: "code/nuget.config"
  dotnet_version: "8.0.x"
  container_name: reporting

resources:
  repositories:
    - repository: templates
      type: git
      name: JE_Backend/ent-pipelines
      ref: 'refs/heads/develop'
    - repository: helm
      type: git
      name: JE_Backend/eks-workloads
      ref: 'refs/heads/main'

trigger:
  none

jobs:
- job: Build_service
  pool:
    vmImage: 'ubuntu-latest'

  variables:
  - group: SonarQube

  steps:
  - checkout: self
    fetchDepth: 0
    path: s/code
  - checkout: helm
    path: s/tool

  - template: template_versioning.yml@templates
    parameters:
      ci_folder: "code/ci-build"
      solution_folder: "$(Agent.BuildDirectory)/s/code"

  - template: template_dotnet_test.yml@templates
    parameters:
      project: ${{ variables.project }}
      dotnet_version: ${{ variables.dotnet_version }}
      nuget_config: ${{ variables.nuget_config }}
      output_folder: '$(Build.SourcesDirectory)/code/app/'
      container_name: ${{ variables.container_name }}

