using BlueGuava.Extensions.Paging;
using BlueGuava.Reporting.Common.Models;

namespace BlueGuava.Reporting.Common.Repositories;

public interface IReportRepository<T> : IPagedSearch<EntityFilter, T> where T : ReportBase
{
    Task<T> Create(T entity);
    System.Threading.Tasks.Task Update(T entity);
    Task<T?> Retrieve(string id);
    System.Threading.Tasks.Task Delete(string id);
    IAsyncEnumerable<T> Search(EntityFilter searchArgs, bool ascending, CancellationToken cancellationToken = default);
    System.Threading.Tasks.Task InvalidateReports(string hashKey);
}