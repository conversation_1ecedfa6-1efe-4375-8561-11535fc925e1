using BlueGuava.Extensions.Paging;
using BlueGuava.Reporting.Common.Models.Entities.EventBridge;

namespace BlueGuava.Reporting.Common.Repositories;

public interface IReportSetupRepository : IPagedSearch<ReportSetupFilter, ReportSetup>
{
    public Task<ReportSetup> Create(ReportSetupRequest entity);
    public System.Threading.Tasks.Task Update(ReportSetup entity);
    public System.Threading.Tasks.Task Delete(string id);
    public Task<ReportSetup?> Retrieve(string id);
    public Task<IEnumerable<ReportSetup>> RetrieveAll();
}