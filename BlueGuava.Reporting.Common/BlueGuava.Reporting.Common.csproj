<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <LangVersion>12</LangVersion>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="AutoMapper" Version="14.0.0" />
      <PackageReference Include="BlueGuava.EventStream.Common" Version="8.1.1" />
      <PackageReference Include="BlueGuava.Extensions.AspNetCore.Authentication" Version="8.1.1" />
      <PackageReference Include="BlueGuava.Extensions.AsyncEnumerable" Version="8.1.1" />
      <PackageReference Include="BlueGuava.Extensions.AWS.EventStream.Producer" Version="8.1.2" />
      <PackageReference Include="BlueGuava.Extensions.Logging" Version="8.1.1" />
      <PackageReference Include="BlueGuava.Extensions.Paging.Abstractions" Version="8.1.1" />
      <PackageReference Include="BlueGuava.Extensions.Telemetry" Version="8.1.1" />
      <PackageReference Include="BlueGuava.ItemsProcessing.Abstractions" Version="8.1.1" />
      <PackageReference Include="BlueGuava.MessageQueuing.Abstractions" Version="8.1.1" />
      <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.2" />
      <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.2" />
      <PackageReference Include="Microsoft.FeatureManagement" Version="4.0.0" />
      <PackageReference Include="BlueGuava.Extensions.Configuration" Version="8.1.1" />
      <PackageReference Include="BlueGuava.Extensions.Configuration.ParameterStore" Version="8.1.1" />
      <PackageReference Include="BlueGuava.JobManagement.Common" Version="8.1.5" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\BlueGuava.Reporting.Messages\BlueGuava.Reporting.Messages.csproj" />
    </ItemGroup>

</Project>
