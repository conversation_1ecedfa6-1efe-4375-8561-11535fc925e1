using BlueGuava.Extensions.Paging;
using BlueGuava.Reporting.Common.Models;

namespace BlueGuava.Reporting.Common.Services;

public interface IReportService<T> where T : ReportBase, new()
{
    Task<T> Create(T entity);
    Task<T> Update(T entity);
    Task<T?> Retrieve(string id);
    System.Threading.Tasks.Task Delete(string id);
    IAsyncEnumerable<T> Search(EntityFilter searchArgs, CancellationToken cancellationToken = default);
    Task<PagedResult<T>> StartPaging(EntityFilter searchArgs, int itemLimit, bool ascending);
    Task<PagedResult<T>> GetNextPage(string pagingToken);
    System.Threading.Tasks.Task InvalidateReports(string hashKey);
    Task<List<T>> GetLastAvailableVersion(EntityFilter searchArgs);
}