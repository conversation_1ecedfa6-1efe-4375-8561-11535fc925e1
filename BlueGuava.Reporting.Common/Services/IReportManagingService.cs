using BlueGuava.Reporting.Common.Models.Entities.EventBridge;

namespace BlueGuava.Reporting.Common.Services;

public interface IReportManagingService
{
    public Task<IEnumerable<string>> GetReportNames();
    public Task<IEnumerable<ReportSetup>> GetReportSetups();
    public Task<ReportSetup> GetReportSetup(string reportSetupId);
    public Task<ReportSetup> CreateReportSetup(ReportSetupRequest reportSetup);
    public Task<ReportSetup> ModifyReportSetup(string reportSetupId, ReportSetupRequest reportSetup);
    public Task<UnifiedSchedulerResponse> DisableReportSetup(string reportSetupId);
    public Task<UnifiedSchedulerResponse> EnableReportSetup(string reportSetupId);
    public Task<UnifiedSchedulerResponse> DeleteReportSetup(string reportSetupId);
    public System.Threading.Tasks.Task ExecuteReportSetup(string reportSetupId);
}