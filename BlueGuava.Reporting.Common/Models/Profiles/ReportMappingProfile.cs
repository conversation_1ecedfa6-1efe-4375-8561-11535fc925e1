using AutoMapper;
using BlueGuava.Reporting.Messages;

namespace BlueGuava.Reporting.Common.Models.Profiles;

public class ReportMappingProfile : Profile
{
    public ReportMappingProfile()
    {
        //Transform to public DTO from internal model
        CreateMap<ReportMessage, ReportBase>()
            .ForMember(dest => dest.Date, opt => opt.MapFrom(src => src.TimeStamp.FormatTimestamp(src.GetType().Name)))
            .IncludeAllDerived();

        var reportMessageTypes = typeof(ReportMessage).GetAllSubclasses();

        var reportTypes = typeof(ReportBase).GetAllSubclasses();

        var messageReportPairs = reportTypes.Join(reportMessageTypes,
            rt => rt.Name,
            mt => mt.Name[..^7],
            (rt, mt) => new { Message = mt, Report = rt });

        foreach (var pair in messageReportPairs)
        {
            var createMap = typeof(Profile).GetMethod("CreateMap", 2, Array.Empty<Type>())?.MakeGenericMethod(pair.Message, pair.Report);

            createMap?.Invoke(this, null);
        }
    }
}