using System.Reflection;
using AutoMapper;
using BlueGuava.EventStream.Common.Model;
using BlueGuava.Reporting.Messages.Entities;

namespace BlueGuava.Reporting.Common.Models.Profiles;

public class PropertiesToReportDataContractConverter<T> :
    ITypeConverter<PropertyListReportMessage, T> where T : class, IDataContract, new()
{
    public T Convert(PropertyListReportMessage? source, T destination, ResolutionContext context)
    {
        // return if nothing to update
        if (!(source?.Properties?.Count > 0)) return destination;

        T? result = null;
        var properties = typeof(T).GetProperties().ToDictionary(p => p.Name);
        foreach (var data in source.Properties.Where(x => x.Name != "Type"))
        {
            if (!properties.TryGetValue(data.Name ?? "", out var property)) continue;
            result ??= new T();

            try
            {
                SetValue(result, property, data);
            }
            catch
            {
                ///DO nothing, try to parse
            }
        }

        return result ?? destination;
    }

    private static void SetValue(T destination, PropertyInfo property, Property data)
    {
        var enumCandidate = Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType;
        if (enumCandidate.IsEnum)
        {
            var value = Enum.Parse(enumCandidate, data.Value?.ToString()!);
            property.SetValue(destination, value);
        }
        else
        {
            var convertedValue = (data.Value == null) ? null
                : ChangeType(data.Value, property.PropertyType);
            property.SetValue(destination, convertedValue, null);
        }
    }

    private static object? ChangeType(object value, Type conversion)
    {
        var t = conversion;

        if (t.IsGenericType && t.GetGenericTypeDefinition() == typeof(Nullable<>))
        {
            if (value == null) return null;
            t = Nullable.GetUnderlyingType(t)!;
        }

        return System.Convert.ChangeType(value, t);
    }
}