namespace BlueGuava.Reporting.Common.Models.Entities.EventBridge;

public class ReportProcessorFargateTaskSettings
{
    public required string Arn { get; set; }
    public required string RoleArn { get; set; }
    public required string ContainerName { get; set; }
    public required string Subnets { get; set; }
    public required string SecurityGroups { get; set; }
    public required string TaskDefinitionArn { get; set; }
    public required string DeadLetterArn { get; set; }
}