﻿
Microsoft Visual Studio Solution File, Format Version 12.00
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BlueGuava.Reporting.Api", "BlueGuava.Reporting.Api\BlueGuava.Reporting.Api.csproj", "{84D65103-321E-4B13-936E-A41A10B4A5FE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BlueGuava.Reporting.Services", "BlueGuava.Reporting.Services\BlueGuava.Reporting.Services.csproj", "{2AD8A888-B5B8-4E5A-AC8D-4F55F5D30FE7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BlueGuava.Reporting.Common", "BlueGuava.Reporting.Common\BlueGuava.Reporting.Common.csproj", "{0E88BD6B-16DA-453D-A43D-218806F8C63A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BlueGuava.Reporting.Repository.DynamoDb", "BlueGuava.Reporting.Repository.DynamoDb\BlueGuava.Reporting.Repository.DynamoDb.csproj", "{6593C9EC-260E-44D8-ABE0-DDF5A0AE7C8F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BlueGuava.Reporting.Repository.S3", "BlueGuava.Reporting.Repository.S3\BlueGuava.Reporting.Repository.S3.csproj", "{BF3A5CF1-1A71-4B30-8FCB-ED2219A92463}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Packages", "Packages", "{B7DE04A2-7532-4EE4-81A4-E1D5179205A1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BlueGuava.Reporting.Messages", "BlueGuava.Reporting.Messages\BlueGuava.Reporting.Messages.csproj", "{9F3F60EE-CBA4-4316-8A4F-C486874915D4}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{0F5AF28A-740B-4CD7-9DCF-42747F7CA0F8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BlueGuava.Reporting.UnitTests", "BlueGuava.Reporting.UnitTests\BlueGuava.Reporting.UnitTests.csproj", "{4DDA9E6E-D056-459D-A7AB-994421A28EA2}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{84D65103-321E-4B13-936E-A41A10B4A5FE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{84D65103-321E-4B13-936E-A41A10B4A5FE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{84D65103-321E-4B13-936E-A41A10B4A5FE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{84D65103-321E-4B13-936E-A41A10B4A5FE}.Release|Any CPU.Build.0 = Release|Any CPU
		{2AD8A888-B5B8-4E5A-AC8D-4F55F5D30FE7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2AD8A888-B5B8-4E5A-AC8D-4F55F5D30FE7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2AD8A888-B5B8-4E5A-AC8D-4F55F5D30FE7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2AD8A888-B5B8-4E5A-AC8D-4F55F5D30FE7}.Release|Any CPU.Build.0 = Release|Any CPU
		{0E88BD6B-16DA-453D-A43D-218806F8C63A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0E88BD6B-16DA-453D-A43D-218806F8C63A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0E88BD6B-16DA-453D-A43D-218806F8C63A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0E88BD6B-16DA-453D-A43D-218806F8C63A}.Release|Any CPU.Build.0 = Release|Any CPU
		{6593C9EC-260E-44D8-ABE0-DDF5A0AE7C8F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6593C9EC-260E-44D8-ABE0-DDF5A0AE7C8F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6593C9EC-260E-44D8-ABE0-DDF5A0AE7C8F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6593C9EC-260E-44D8-ABE0-DDF5A0AE7C8F}.Release|Any CPU.Build.0 = Release|Any CPU
		{BF3A5CF1-1A71-4B30-8FCB-ED2219A92463}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BF3A5CF1-1A71-4B30-8FCB-ED2219A92463}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BF3A5CF1-1A71-4B30-8FCB-ED2219A92463}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BF3A5CF1-1A71-4B30-8FCB-ED2219A92463}.Release|Any CPU.Build.0 = Release|Any CPU
		{9F3F60EE-CBA4-4316-8A4F-C486874915D4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9F3F60EE-CBA4-4316-8A4F-C486874915D4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9F3F60EE-CBA4-4316-8A4F-C486874915D4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9F3F60EE-CBA4-4316-8A4F-C486874915D4}.Release|Any CPU.Build.0 = Release|Any CPU
		{4DDA9E6E-D056-459D-A7AB-994421A28EA2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4DDA9E6E-D056-459D-A7AB-994421A28EA2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4DDA9E6E-D056-459D-A7AB-994421A28EA2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4DDA9E6E-D056-459D-A7AB-994421A28EA2}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{9F3F60EE-CBA4-4316-8A4F-C486874915D4} = {B7DE04A2-7532-4EE4-81A4-E1D5179205A1}
		{4DDA9E6E-D056-459D-A7AB-994421A28EA2} = {0F5AF28A-740B-4CD7-9DCF-42747F7CA0F8}
	EndGlobalSection
EndGlobal
