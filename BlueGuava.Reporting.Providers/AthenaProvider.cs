using BlueGuava.EventStream.Processor.Services;
using BlueGuava.Reporting.Common.Providers;
using CorrelationId.Abstractions;
using Microsoft.Extensions.Logging;

namespace BlueGuava.Reporting.Providers;

public class AthenaProvider : IAthenaProvider
{
    private readonly ILogger<AthenaProvider> logger;
    private readonly ICorrelationContextAccessor correlationContextAccessor;
    private readonly IDataProcessor dataProcessor;

    public AthenaProvider(
        ILogger<AthenaProvider> logger,
        ICorrelationContextAccessor correlationContextAccessor,
        IDataProcessor dataProcessor)
    {
        this.logger = logger;
        this.correlationContextAccessor = correlationContextAccessor;
        this.dataProcessor = dataProcessor;
    }

    public async Task StartQueryExecutionAsync()
    {
        await dataProcessor.QueryExecution("select * from classification_content");
    }
    
    public async Task ProcessQueryResultAsync(string queryExecutionId)
    {
        var result = await dataProcessor.ProcessQueryResult(queryExecutionId);
    }
}