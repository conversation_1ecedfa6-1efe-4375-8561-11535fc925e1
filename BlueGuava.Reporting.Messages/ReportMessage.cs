using System;
using BlueGuava.MessageQueuing;

namespace BlueGuava.Reporting.Messages
{

    public abstract class ReportMessage : IQueueItem
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string? Receipt { get; set; }
        public string? ProjectId { get; set; }
        public string? Sender { get; set; }

        public DateTime? TimeStamp { get; set; }
        public DateTime? RunDate { get; set; }

        string? IQueueItem.GroupId { get => Id; set => _ = value; }
        string? IQueueItem.DeduplicationId { get => Id; set => _ = value; }
    }
}