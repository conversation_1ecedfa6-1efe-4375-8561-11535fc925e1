namespace BlueGuava.Reporting.Messages.Entities.ClientReports
{
    public class DailyContentConsumptionWriteBackMessage : ReportMessage
    {
        public string? Hit { get; set; }
        public string? Played { get; set; }
        public string? SpentTime { get; set; }
        public string? TimeFrame { get; set; }
        public string? EntityId { get; set; }
        public long Quantity { get; set; }
    }
}