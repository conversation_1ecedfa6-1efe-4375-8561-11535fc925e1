﻿using BlueGuava.Reporting.Common.Monitoring;

namespace BlueGuava.Reporting.Api.Monitoring
{
    public class MetricsInitializer : IHostedService
    {
        private readonly IMetricsCollector metrics;
        public MetricsInitializer(IMetricsCollector metrics)
        {
            this.metrics = metrics;
        }

        public System.Threading.Tasks.Task StartAsync(CancellationToken cancellationToken)
        {
            metrics.InitializeCounters();
            return System.Threading.Tasks.Task.CompletedTask;
        }

        public System.Threading.Tasks.Task StopAsync(CancellationToken cancellationToken)
        {
            return System.Threading.Tasks.Task.CompletedTask;
        }
    }
}
