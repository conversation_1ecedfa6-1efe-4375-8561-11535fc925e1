﻿<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <LangVersion>12</LangVersion>
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <Version>1.0.0</Version>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AWSSDK.SecurityToken" Version="3.7.401.53" />
        <PackageReference Include="BlueGuava.Extensions.AspNetCore.VersionController" Version="8.1.1" />
        <PackageReference Include="BlueGuava.Extensions.Authentication.Swagger" Version="8.1.1" />
        <PackageReference Include="BlueGuava.Extensions.AWS.MessageQueuing.Amazon" Version="8.1.2" />
        <PackageReference Include="BlueGuava.Extensions.AspNetCore.ExceptionHandling" Version="8.1.1" />
        <PackageReference Include="BlueGuava.ItemsProcessing" Version="8.1.1" />
        <PackageReference Include="BlueGuava.ItemsProcessing.XRayExtension" Version="8.1.1" />
        <PackageReference Include="BlueGuava.MessageQueuing.Worker" Version="8.1.1" />
        <PackageReference Include="CsvHelper" Version="33.0.1" />
        <PackageReference Include="prometheus-net.AspNetCore" Version="8.2.1" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="7.3.1" />
        <PackageReference Include="Asp.Versioning.Mvc" Version="8.1.0" />
        <PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.1.0" />
        <PackageReference Include="BlueGuava.Extensions.AwsSwaggerInteraction" Version="8.1.1" />
        <PackageReference Include="BlueGuava.Extensions.Swagger.ApiVersioning" Version="8.1.1" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\BlueGuava.Reporting.Common\BlueGuava.Reporting.Common.csproj" />
        <ProjectReference Include="..\BlueGuava.Reporting.Repository.DynamoDb\BlueGuava.Reporting.Repository.DynamoDb.csproj" />
        <ProjectReference Include="..\BlueGuava.Reporting.Repository.S3\BlueGuava.Reporting.Repository.S3.csproj" />
        <ProjectReference Include="..\BlueGuava.Reporting.Services\BlueGuava.Reporting.Services.csproj" />
    </ItemGroup>

</Project>