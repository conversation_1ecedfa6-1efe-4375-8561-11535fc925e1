using Microsoft.AspNetCore.Mvc.Routing;

namespace BlueGuava.Reporting.Api;

public class GetReportRouteAttribute : HttpMethodAttribute
{
    private static readonly IEnumerable<string> SupportedMethods = new[] { "GET" };

    /// <summary>
    /// Creates a new <see cref="GetReportRouteAttribute"/> with the given report name.
    /// </summary>
    /// <param name="reportName">The report name. May not be null.</param>
    public GetReportRouteAttribute(string reportName) : base(SupportedMethods, TransformReportNameIntoRoute(reportName))
    {
        if (reportName == null)
        {
            throw new ArgumentNullException(nameof(reportName));
        }
    }

    private static string TransformReportNameIntoRoute(string reportName)
    {
        if (reportName.Contains("Daily"))
        {
            return reportName.Replace("Daily", "Daily/");
        }

        if (reportName.Contains("Aggregated"))
        {
            return reportName.Replace("Aggregated", "Aggregated/");
        }

        return reportName.Contains("WeeklyList") || reportName.Contains("ConcurrentVisitorsCounter")
            ? reportName
            : throw new ArgumentOutOfRangeException(nameof(reportName));
    }
}