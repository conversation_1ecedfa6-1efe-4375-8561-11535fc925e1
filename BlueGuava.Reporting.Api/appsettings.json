{"Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Error", "System": "Error", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.AspNetCore.Hosting.Diagnostics": "Error", "AspNetCore.HealthChecks.UI": "Warning", "HealthChecks": "Warning", "Microsoft.AspNetCore.Authentication": "Information"}}, "Enrich": ["FromLogContext", "With", "WithExceptionDetails", "WithCorrelationId"], "Using": ["Serilog.Sinks.Console"], "WriteTo": {"0": {"Name": "<PERSON><PERSON><PERSON>", "Args": {"formatter": {"type": "Serilog.Formatting.Json.Json<PERSON>, Ser<PERSON>g", "renderMessage": true}}}}}, "AllowedHosts": "*", "XRay": {"DisableXRayTracing": "true"}}