using AutoMapper;
using BlueGuava.Reporting.Common.Models.Entities.ClientReports;
using BlueGuava.Reporting.Common.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OutputCaching;

namespace BlueGuava.Reporting.Api.Controllers;

/// <summary>
/// Contains methods to retrieve reports with consumption metrics.
/// </summary>
[ApiVersion("1.0")]
[ApiController]
[Route("api/v{version:apiVersion}/[controller]")]
[OutputCache(PolicyName = "OutputCache")]
public class ConsumptionMetricsController : BaseController
{
    /// <inheritdoc />
    public ConsumptionMetricsController(IMapper mapper) : base(
        mapper)
    {
    }

    /// <summary>
    /// Retrieves names of reports main purpose of which to give general info about the application for particular period
    /// of time. Mostly contain total and average values of some events.
    /// </summary>
    /// <returns><see cref="OkObjectResult"/> with list of reports names.</returns>
    [HttpGet("Schema/[action]")]
    public ActionResult GeneralPeriodicReports()
    {
        var result = new List<string>
        {
            "DailyTotalPlaybackDevice",
            "AggregatedTotalPlaybackDevice",
            "DailyDigitalPerformance",
            "AggregatedDigitalPerformance",
            "DailyWatchedTime",
            "AggregatedWatchedTime"
        };

        return Ok(result);
    }

    /// <summary>
    /// Retrieves names of reports calculated for particular content unit. Reports contain contentId as required parameter.
    /// </summary>
    /// <returns><see cref="OkObjectResult"/> with list of reports names.</returns>
    [HttpGet("Schema/[action]")]
    public ActionResult SingleContentReports()
    {
        var result = new List<string>
        {
            "DailyNumberOfContentAccess",
            "AggregatedNumberOfContentAccess",
            "DailyContentQualityTime",
            "AggregatedContentQualityTime",
            "DailyPlaybackDevice",
            "AggregatedPlaybackDevice"
        };

        return Ok(result);
    }

    /// <summary>
    /// Retrieves names of reports calculated for particular product unit. Reports contain productId as required parameter.
    /// </summary>
    /// <returns><see cref="OkObjectResult"/> with list of reports names.</returns>
    [HttpGet("Schema/[action]")]
    public ActionResult SingleProductReports()
    {
        var result = new List<string>
        {
            "DailyLoginPlayback",
            "AggregatedLoginPlayback"
        };

        return Ok(result);
    }

    /// <summary>
    /// Retrieves names of reports calculated for particular carousel. Reports contain carouselId as required parameter.
    /// </summary>
    /// <returns><see cref="OkObjectResult"/> with list of reports names.</returns>
    [HttpGet("Schema/[action]")]
    public ActionResult SingleCarouselReports()
    {
        var result = new List<string>
        {
            "DailyPlaybackRate",
            "AggregatedPlaybackRate"
        };

        return Ok(result);
    }

    /// <summary>
    /// Retrieves daily version of NumberOfContentAccess report. Report shows the total number of playbacks of certain content on day to day basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyNumberOfContentAccess"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="contentId">Identifier of content the report retrieved for.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyNumberOfContentAccess"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyNumberOfContentAccess)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyNumberOfContentAccess> logger,
        [FromServices] IReportService<DailyNumberOfContentAccess> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        [FromQuery] string contentId,
        CancellationToken cancellationToken)
    {
        return await ProcessBaseReport(logger, reportService, from, to, cancellationToken, contentId);
    }

    /// <summary>
    /// Retrieves aggregated version of NumberOfContentAccess report. Report shows the total number of playbacks of certain content on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedNumberOfContentAccess"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="contentId">Identifier of content the report retrieved for.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedNumberOfContentAccess"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedNumberOfContentAccess)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedNumberOfContentAccess> logger,
        [FromServices] IReportService<AggregatedNumberOfContentAccess> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        [FromQuery] string contentId,
        CancellationToken cancellationToken)
    {
        return await ProcessBaseReport(logger, reportService, from, to, cancellationToken, contentId);
    }

    /// <summary>
    /// Retrieves daily version of ContentQualityTime report. Report shows the total duration of playbacks of particular content on speed equals 1 per day.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyContentQualityTime"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="contentId">Identifier of content the report retrieved for.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyContentQualityTime"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyContentQualityTime)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyContentQualityTime> logger,
        [FromServices] IReportService<DailyContentQualityTime> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        [FromQuery] string contentId,
        CancellationToken cancellationToken)
    {
        return await ProcessBaseReport(logger, reportService, from, to, cancellationToken, contentId);
    }

    /// <summary>
    /// Retrieves aggregated version of ContentQualityTime report. Report shows the total duration of playbacks of particular content on speed equals 1 on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedContentQualityTime"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="contentId">Identifier of content the report retrieved for.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedContentQualityTime"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedContentQualityTime)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedContentQualityTime> logger,
        [FromServices] IReportService<AggregatedContentQualityTime> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        [FromQuery] string contentId,
        CancellationToken cancellationToken)
    {
        return await ProcessBaseReport(logger, reportService, from, to, cancellationToken, contentId);
    }


    /// <summary>
    /// Retrieves daily version of PlaybackDevice report. The report shows the total number of plays of of certain
    /// content grouped by device type/sub-type per day.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyPlaybackDevice"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="contentId">Identifier of content the report retrieved for.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyPlaybackDevice"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyPlaybackDevice)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyPlaybackDevice> logger,
        [FromServices] IReportService<DailyPlaybackDevice> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        [FromQuery] string contentId,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken, contentId);
    }

    /// <summary>
    /// Retrieves aggregated version of PlaybackDevice report. The report shows the total number of plays of of certain
    /// content grouped by device type/sub-type on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedPlaybackDevice"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="contentId">Identifier of content the report retrieved for.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedPlaybackDevice"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedPlaybackDevice)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedPlaybackDevice> logger,
        [FromServices] IReportService<AggregatedPlaybackDevice> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        [FromQuery] string contentId,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken, contentId);
    }

    /// <summary>
    /// Retrieves daily version of TotalPlaybackDevice report. The report shows the total number of plays of all content
    /// grouped by device type/sub-type per day.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyTotalPlaybackDevice"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyTotalPlaybackDevice"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyTotalPlaybackDevice)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyTotalPlaybackDevice> logger,
        [FromServices] IReportService<DailyTotalPlaybackDevice> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves aggregated version of TotalPlaybackDevice report. The report shows the total number of plays of all content
    /// grouped by device type/sub-type on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedTotalPlaybackDevice"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedTotalPlaybackDevice"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedTotalPlaybackDevice)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedTotalPlaybackDevice> logger,
        [FromServices] IReportService<AggregatedTotalPlaybackDevice> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves daily version of PlaybackDevice report. The report shows how many customers subscribed to particular
    /// product logged at certain time period and how many of them started playback. Report is broken down by country
    /// and type of subscriber.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyPlaybackDevice"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="productId">Identifier of product the report retrieved for.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyPlaybackDevice"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyLoginPlayback)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyLoginPlayback> logger,
        [FromServices] IReportService<DailyLoginPlayback> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        [FromQuery] string productId,
        CancellationToken cancellationToken)
    {
        return await ProcessListDictionaryBaseReport(logger, reportService, from, to, cancellationToken, productId);
    }

    /// <summary>
    /// Retrieves aggregated version of PlaybackDevice report. The report shows how many customers subscribed to particular
    /// product logged at certain time period and how many of them started playback on month to date basis. Report is
    /// broken down by country and type of subscriber.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedPlaybackDevice"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="productId">Identifier of product the report retrieved for.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedPlaybackDevice"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedLoginPlayback)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedLoginPlayback> logger,
        [FromServices] IReportService<AggregatedLoginPlayback> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        [FromQuery] string productId,
        CancellationToken cancellationToken)
    {
        return await ProcessListDictionaryBaseReport(logger, reportService, from, to, cancellationToken, productId);
    }

    /// <summary>
    /// Retrieves daily version of DigitalPerformance report. The report shows the total duration of playbacks
    /// (in seconds) grouped by content genre per day.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyDigitalPerformance"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyDigitalPerformance"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyDigitalPerformance)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyDigitalPerformance> logger,
        [FromServices] IReportService<DailyDigitalPerformance> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves aggregated version of DigitalPerformance report. The report shows the total duration of playbacks
    /// (in seconds) grouped by content genre on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedDigitalPerformance"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedDigitalPerformance"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedDigitalPerformance)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedDigitalPerformance> logger,
        [FromServices] IReportService<AggregatedDigitalPerformance> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves daily version of WatchedTime report. The report shows the TOP-20 of content by the total duration
    /// of playbacks (in seconds) on day to day basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyWatchedTime"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyWatchedTime"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyWatchedTime)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyWatchedTime> logger,
        [FromServices] IReportService<DailyWatchedTime> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves aggregated version of WatchedTime report. The report shows the TOP-20 of content by the total duration
    /// of playbacks (in seconds) on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedWatchedTime"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedWatchedTime"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedWatchedTime)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedWatchedTime> logger,
        [FromServices] IReportService<AggregatedWatchedTime> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves daily version of PlaybackRate report. The report shows the total number of how many playback calls
    /// were made from particular carousel (catalog) on day to day basis. Report is broken down by country, subscription
    /// and device.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyPlaybackRate"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="carouselId">Identifier of carousel the report retrieved for.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyPlaybackRate"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyPlaybackRate)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyPlaybackRate> logger,
        [FromServices] IReportService<DailyPlaybackRate> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        [FromQuery] string carouselId,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken, carouselId);
    }

    /// <summary>
    /// Retrieves aggregated version of PlaybackRate report. The report shows the total number of how many playback calls
    /// were made from particular carousel (catalog) on month to date basis. Report is broken down by country, subscription
    /// and device.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedPlaybackRate"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="carouselId">Identifier of carousel the report retrieved for.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedPlaybackRate"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedPlaybackRate)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedPlaybackRate> logger,
        [FromServices] IReportService<AggregatedPlaybackRate> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        [FromQuery] string carouselId,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken, carouselId);
    }
}