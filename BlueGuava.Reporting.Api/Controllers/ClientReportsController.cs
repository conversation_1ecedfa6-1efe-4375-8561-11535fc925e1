using AutoMapper;
using BlueGuava.Reporting.Api.Models;
using BlueGuava.Reporting.Common.Models;
using BlueGuava.Reporting.Common.Models.Entities.ClientReports;
using BlueGuava.Reporting.Common.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OutputCaching;

namespace BlueGuava.Reporting.Api.Controllers;

/// <summary>
/// Contains methods to retrieve client reports.
/// </summary>
[ApiVersion("1.0")]
[ApiController]
[Route("api/v{version:apiVersion}/[controller]")]
[OutputCache(PolicyName = "OutputCache")]
public class ClientReportsController : BaseController
{
    /// <inheritdoc />
    public ClientReportsController(IMapper mapper) : base(mapper)
    {
    }

    /// <summary>
    /// Retrieves names of reports calculated for particular content unit. Reports contain contentId as required parameter.
    /// </summary>
    /// <returns><see cref="OkObjectResult"/> with list of reports names.</returns>
    [HttpGet("Schema/[action]")]
    public ActionResult SingleContentReports()
    {
        var result = new List<string>
        {
            "DailyTotalWatchMinutes",
            "DailyContentConsumption",
            "DailyContentConsumptionHeatMap",
        };

        return Ok(result);
    }

    /// <summary>
    /// Retrieves names of reports calculated for particular user in Customer or Creator role. Reports contain user's Id as required parameter.
    /// </summary>
    /// <returns><see cref="OkObjectResult"/> with list of reports names.</returns>
    [HttpGet("Schema/[action]")]
    public ActionResult SinglePersonContentRelatedReports()
    {
        var result = new List<string>
        {
            "DailyCustomerConsumption",
            "DailyContentByCreatorsConsumption"
        };

        return Ok(result);
    }

    /// <summary>
    /// Retrieves names of reports main purpose of which to give general info about the application for particular period
    /// of time. Mostly contain total and average values of some events.
    /// </summary>
    /// <returns><see cref="OkObjectResult"/> with list of reports names.</returns>
    [HttpGet("Schema/[action]")]
    public ActionResult GeneralPeriodicReports()
    {
        var result = new List<string>
        {
            "DailyTotalUsers",
            "AggregatedTotalUsers",
            "DailyTotalContentWatched",
            "AggregatedTotalContentWatched",
            "DailyTotalContentWatchedP50",
            "DailyTotalContentWatchedP90",
            "DailyBestContentWatched",
            "AggregatedBestContentWatched",
            "DailyWorstContentWatched",
            "AggregatedWorstContentWatched",
            "TotalAggregated",
            "DailyTotalContentTimeSpent",
            "AggregatedTotalContentTimeSpent"
        };

        return Ok(result);
    }

    /// <summary>
    /// Retrieves daily version of TotalWatchMinutes report. The report shows how much time customers spent watching
    /// particular content during one day (in seconds).
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyTotalWatchMinutes"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="contentId">Content identifier.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyTotalWatchMinutes"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyTotalWatchMinutes)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyTotalWatchMinutes> logger,
        [FromServices] IReportService<DailyTotalWatchMinutes> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        [FromQuery] string contentId,
        CancellationToken cancellationToken)
    {
        return await ProcessBaseReport(logger, reportService, from, to, cancellationToken, contentId);
    }

    /// <summary>
    /// Retrieves daily version of CustomerConsumption report. The report shows how many times particular customer
    /// started playing any content during one day.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyCustomerConsumption"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="customerId">Customer identifier.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyCustomerConsumption"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyCustomerConsumption)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyCustomerConsumption> logger,
        [FromServices] IReportService<DailyCustomerConsumption> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        [FromQuery] string customerId,
        CancellationToken cancellationToken)
    {
        return await ProcessBaseReport(logger, reportService, from, to, cancellationToken, customerId);
    }

    /// <summary>
    /// Retrieves daily version of ContentConsumption report. The report shows how many times particular content was
    /// played by any customer during one day.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyContentConsumption"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="contentId">Content identifier.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyContentConsumption"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyContentConsumption)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyContentConsumption> logger,
        [FromServices] IReportService<DailyContentConsumption> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        [FromQuery] string contentId,
        CancellationToken cancellationToken)
    {
        return await ProcessBaseReport(logger, reportService, from, to, cancellationToken, contentId);
    }

    /// <summary>
    /// Retrieves daily version of ContentByCreatorsConsumption report. The report shows how many times content
    /// created by particular customer was played by any customer during one day.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyContentByCreatorsConsumption"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="creatorId">Creator identifier.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyContentByCreatorsConsumption"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyContentByCreatorsConsumption)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyContentByCreatorsConsumption> logger,
        [FromServices] IReportService<DailyContentByCreatorsConsumption> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        [FromQuery] string creatorId,
        CancellationToken cancellationToken)
    {
        return await ProcessBaseReport(logger, reportService, from, to, cancellationToken, creatorId);
    }

    /// <summary>
    /// Retrieves daily version of ContentConsumptionHeatMap report. The report shows particular content consumption in
    /// a “heat-map”-like way on daily basis. Data returned in percents divided into 10 time-period percentiles based
    /// on the content duration.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyContentConsumptionHeatMap"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="contentId">Content identifier.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyContentConsumptionHeatMap"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyContentConsumptionHeatMap)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyContentConsumptionHeatMap> logger,
        [FromServices] IReportService<DailyContentConsumptionHeatMap> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        [FromQuery] string contentId,
        CancellationToken cancellationToken)
    {
        return await ProcessBaseReport(logger, reportService, from, to, cancellationToken, contentId);
    }

    /// <summary>
    /// Retrieves daily version of TotalUsers report. The report shows the number of distinct customers who played content per day.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyTotalUsers"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyTotalUsers"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyTotalUsers)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyTotalUsers> logger,
        [FromServices] IReportService<DailyTotalUsers> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves aggregated version of TotalUsers report. The report shows the number of distinct customers who played
    /// content on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedTotalUsers"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedTotalUsers"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedTotalUsers)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedTotalUsers> logger,
        [FromServices] IReportService<AggregatedTotalUsers> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves daily version of TotalContentWatched report. The report shows the number the number of distinct content units were played per day.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyTotalContentWatched"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyTotalContentWatched"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyTotalContentWatched)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyTotalContentWatched> logger,
        [FromServices] IReportService<DailyTotalContentWatched> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves aggregated version of TotalContentWatched report. The report shows the number the number of distinct
    /// content units were played on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedTotalContentWatched"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedTotalContentWatched"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedTotalContentWatched)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedTotalContentWatched> logger,
        [FromServices] IReportService<AggregatedTotalContentWatched> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves daily version of TotalContentWatchedP50 report. The report shows the number of content which playback
    /// reached the 50% percentile on the consumption heat-map per day.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyTotalContentWatchedP50"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyTotalContentWatchedP50"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyTotalContentWatchedP50)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyTotalContentWatchedP50> logger,
        [FromServices] IReportService<DailyTotalContentWatchedP50> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves daily version of TotalContentWatchedP90 report. The report shows the number of content which playback
    /// reached the 90% percentile on the consumption heat-map per day.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyTotalContentWatchedP90"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyTotalContentWatchedP90"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyTotalContentWatchedP90)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyTotalContentWatchedP90> logger,
        [FromServices] IReportService<DailyTotalContentWatchedP90> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves daily version of BestContentWatched report. The report shows the top-10 content by consumption time spent
    /// (in seconds) in descending order, plus it shows the number content hits per day.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyBestContentWatched"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyBestContentWatched"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyBestContentWatched)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyBestContentWatched> logger,
        [FromServices] IReportService<DailyBestContentWatched> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessListDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves aggregated version of BestContentWatched report. The report shows the top-10 content by consumption
    /// time spent (in seconds) in descending order, plus it shows the number content hits on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedBestContentWatched"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedBestContentWatched"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedBestContentWatched)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedBestContentWatched> logger,
        [FromServices] IReportService<AggregatedBestContentWatched> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessListDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves daily version of WorstContentWatched report. The report shows the worst-10 content by consumption
    /// time spent (in seconds) in ascending order, plus it shows the number content hits per day.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyWorstContentWatched"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyWorstContentWatched"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyWorstContentWatched)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyWorstContentWatched> logger,
        [FromServices] IReportService<DailyWorstContentWatched> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessListDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves aggregated version of WorstContentWatched report. The report shows the worst-10 content by consumption
    /// time spent (in seconds) in ascending order, plus it shows the number content hits on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedWorstContentWatched"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedWorstContentWatched"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedWorstContentWatched)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedWorstContentWatched> logger,
        [FromServices] IReportService<AggregatedWorstContentWatched> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessListDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }


    /// <summary>
    /// Retrieves TotalAggregated report. The report shows shows data from 4 other aggregated reports - AggregatedTotalContentWatchedP90,
    /// AggregatedTotalContentWatchedP50, AggregatedTotalUsers, AggregatedTotalContentWatched. Mentioned reports'
    /// calculations made on a month-to-date basis.
    /// </summary>
    /// <param name="reportServiceP90">Service to fetch AggregatedTotalContentWatchedP90 report.</param>
    /// <param name="reportServiceP50">Service to fetch AggregatedTotalContentWatchedP50 report.</param>
    /// <param name="reportServiceTotalUsers">Service to fetch AggregatedTotalUsers report.</param>
    /// <param name="reportServiceTotalContentWatched">Service to fetch AggregatedTotalContentWatched report.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedWorstContentWatched"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute("TotalAggregated")]
    public async Task<ActionResult> Action(
        [FromServices] IReportService<AggregatedTotalContentWatchedP90> reportServiceP90,
        [FromServices] IReportService<AggregatedTotalContentWatchedP50> reportServiceP50,
        [FromServices] IReportService<AggregatedTotalUsers> reportServiceTotalUsers,
        [FromServices] IReportService<AggregatedTotalContentWatched> reportServiceTotalContentWatched,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        from ??= new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
        var searchOptions = new EntityFilter()
        {
            From = from,
            To = to,
            ReportName = nameof(AggregatedTotalContentWatchedP90)
        };

        var dataResultP90 = new List<AggregatedTotalContentWatchedP90>();
        await foreach (var item in reportServiceP90.Search(searchOptions, cancellationToken))
        {
            dataResultP90.Add(item);
        }

        searchOptions.ReportName = nameof(AggregatedTotalContentWatchedP50);
        var dataResultP50 = new List<AggregatedTotalContentWatchedP50>();
        await foreach (var item in reportServiceP50.Search(searchOptions, cancellationToken))
        {
            dataResultP50.Add(item);
        }

        searchOptions.ReportName = nameof(AggregatedTotalUsers);
        var dataResultTotalUsers = new List<AggregatedTotalUsers>();
        await foreach (var item in reportServiceTotalUsers.Search(searchOptions, cancellationToken))
        {
            dataResultTotalUsers.Add(item);
        }

        searchOptions.ReportName = nameof(AggregatedTotalContentWatched);
        var dataResultTotalContentWatched = new List<AggregatedTotalContentWatched>();
        await foreach (var item in reportServiceTotalContentWatched.Search(searchOptions, cancellationToken))
        {
            dataResultTotalContentWatched.Add(item);
        }

        return Ok(new TotalAggregatedResponseEntity()
        {
            TotalUsers = dataResultTotalUsers?.Sum(x => x.Quantity) ?? 0,
            TotalContentWatched = dataResultTotalContentWatched?.Sum(x => x.Quantity) ?? 0,
            TotalContentWatchedP50 = dataResultP50?.Sum(x => x.Quantity) ?? 0,
            TotalContentWatchedP90 = dataResultP90?.Sum(x => x.Quantity) ?? 0,
        });
    }

    /// <summary>
    /// Retrieves daily version of TotalContentTimeSpent report. The report shows the sum of playback time spent
    /// (in seconds) for all played content per day.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyTotalContentTimeSpent"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyTotalContentTimeSpent"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyTotalContentTimeSpent)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyTotalContentTimeSpent> logger,
        [FromServices] IReportService<DailyTotalContentTimeSpent> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves aggregated version of TotalContentTimeSpent report. The report shows the sum of playback time spent
    /// (in seconds) for all played content on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedTotalContentTimeSpent"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedTotalContentTimeSpent"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedTotalContentTimeSpent)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedTotalContentTimeSpent> logger,
        [FromServices] IReportService<AggregatedTotalContentTimeSpent> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessBaseReport(logger, reportService, from, to, cancellationToken);
    }
}