using AutoMapper;
using BlueGuava.Reporting.Common.Models.Entities.ClientReports;
using BlueGuava.Reporting.Common.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OutputCaching;
using Microsoft.AspNetCore.OutputCaching;

namespace BlueGuava.Reporting.Api.Controllers;

/// <summary>
/// Contains methods to retrieve reports with global-tag metrics.
/// </summary>
[ApiVersion("1.0")]
[ApiController]
[Route("api/v{version:apiVersion}/[controller]")]
[OutputCache(PolicyName = "OutputCache")]
public class GlobalTagMetricsController : BaseController
{
    /// <inheritdoc />
    public GlobalTagMetricsController(IMapper mapper) : base(
        mapper)
    {
    }

    /// <summary>
    /// Retrieves names of reports main purpose of which to give general info about the application for particular period
    /// of time. Mostly contain total and average values of some events.
    /// </summary>
    /// <returns><see cref="OkObjectResult"/> with list of reports names.</returns>
    [HttpGet("Schema/[action]")]
    public ActionResult GeneralPeriodicReports()
    {
        var result = new List<string>
        {
            "DailyGlobalTag",
            "AggregatedGlobalTag"
        };

        return Ok(result);
    }

    /// <summary>
    /// Retrieves daily version of GlobalTag report. The report shows the total number of created tags grouped by their name on day to day basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyGlobalTag"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyGlobalTag"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyGlobalTag)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyGlobalTag> logger,
        [FromServices] IReportService<DailyGlobalTag> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves aggregated version of GlobalTag report. The report shows the total number of created tags grouped by their name on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedGlobalTag"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedGlobalTag"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedGlobalTag)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedGlobalTag> logger,
        [FromServices] IReportService<AggregatedGlobalTag> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }
}