using AutoMapper;
using BlueGuava.Reporting.Common.Models.Entities.ClientReports;
using BlueGuava.Reporting.Common.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OutputCaching;

namespace BlueGuava.Reporting.Api.Controllers;

/// <summary>
/// Contains methods to retrieve reports with content creation metrics.
/// </summary>
[ApiVersion("1.0")]
[ApiController]
[Route("api/v{version:apiVersion}/[controller]")]
[OutputCache(PolicyName = "OutputCache")]
public class ContentCreationMetricsController : BaseController
{
    /// <inheritdoc />
    public ContentCreationMetricsController(IMapper mapper) : base(
        mapper)
    {
    }

    /// <summary>
    /// Retrieves names of reports main purpose of which to give general info about the application for particular period
    /// of time. Mostly contain total and average values of some events.
    /// </summary>
    /// <returns><see cref="OkObjectResult"/> with list of reports names.</returns>
    [HttpGet("Schema/[action]")]
    public ActionResult GeneralPeriodicReports()
    {
        var result = new List<string>
        {
            "DailyNewContentNumber",
            "AggregatedNewContentNumber",
            "DailyNumberOfCameraShare",
            "AggregatedNumberOfCameraShare",
            "DailyTotalLiveChatKpis",
            "AggregatedTotalLiveChatKpis"
        };

        return Ok(result);
    }

    /// <summary>
    /// Retrieves names of reports calculated for particular chat. Reports contain chatId as required parameter.
    /// </summary>
    /// <returns><see cref="OkObjectResult"/> with list of reports names.</returns>
    [HttpGet("Schema/[action]")]
    public ActionResult SingleOrganisationReports()
    {
        var result = new List<string>
        {
            "DailySingleLiveChatKpis",
            "AggregatedSingleLiveChatKpis"
        };

        return Ok(result);
    }

    /// <summary>
    /// Retrieves daily version of NewContentNumber report. Report shows numbers of created content grouped by its type on day to day basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyNewContentNumber"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyNewContentNumber"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyNewContentNumber)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyNewContentNumber> logger,
        [FromServices] IReportService<DailyNewContentNumber> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves aggregated version of NewContentNumber report. Report shows numbers of created content grouped by its type on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedNewContentNumber"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedNewContentNumber"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedNewContentNumber)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedNewContentNumber> logger,
        [FromServices] IReportService<AggregatedNewContentNumber> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves daily version of NumberOfCameraShare report. Report shows the number of streams, attendees and the average length of the discussion in Chime
    ///  chats on day to day basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyNumberOfCameraShare"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyNumberOfCameraShare"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyNumberOfCameraShare)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyNumberOfCameraShare> logger,
        [FromServices] IReportService<DailyNumberOfCameraShare> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves aggregated version of NumberOfCameraShare report. Report shows the number of streams, attendees and the average length of the discussion in Chime
    /// chats on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedNumberOfCameraShare"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedNumberOfCameraShare"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedNumberOfCameraShare)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedNumberOfCameraShare> logger,
        [FromServices] IReportService<AggregatedNumberOfCameraShare> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves daily version of SingleLiveChatKpis report. Report shows the number attendees and total number of comments in particular
    /// live chat on day to day basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailySingleLiveChatKpis"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <param name="chatId">Identifier of chat the report retrieved for.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailySingleLiveChatKpis"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailySingleLiveChatKpis)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailySingleLiveChatKpis> logger,
        [FromServices] IReportService<DailySingleLiveChatKpis> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        [FromQuery] string? chatId,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken, chatId);
    }

    /// <summary>
    /// Retrieves aggregated version of SingleLiveChatKpis report. Report shows the number attendees and total number of comments in particular
    /// live chat on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedSingleLiveChatKpis"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <param name="chatId">Identifier of chat the report retrieved for.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedSingleLiveChatKpis"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedSingleLiveChatKpis)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedSingleLiveChatKpis> logger,
        [FromServices] IReportService<AggregatedSingleLiveChatKpis> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        [FromQuery] string chatId,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken, chatId);
    }

    /// <summary>
    /// Retrieves daily version of TotalLiveChatKpis report. Report shows the number of chat, attendees and total number of comments in live
    /// chats on day to day basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyTotalLiveChatKpis"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyTotalLiveChatKpis"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyTotalLiveChatKpis)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyTotalLiveChatKpis> logger,
        [FromServices] IReportService<DailyTotalLiveChatKpis> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves aggregated version of TotalLiveChatKpis report. Report shows the number of chat, attendees and total number of comments in live
    /// chats on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedTotalLiveChatKpis"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedTotalLiveChatKpis"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedTotalLiveChatKpis)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedTotalLiveChatKpis> logger,
        [FromServices] IReportService<AggregatedTotalLiveChatKpis> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }
}