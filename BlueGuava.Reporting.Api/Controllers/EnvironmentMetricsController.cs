using AutoMapper;
using BlueGuava.Reporting.Common.Models.Entities.ClientReports;
using BlueGuava.Reporting.Common.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OutputCaching;

namespace BlueGuava.Reporting.Api.Controllers;

/// <summary>
/// Contains methods to retrieve reports with environment metrics.
/// </summary>
[ApiVersion("1.0")]
[ApiController]
[Route("api/v{version:apiVersion}/[controller]")]
[OutputCache(PolicyName = "OutputCache")]
public class EnvironmentMetricsController : BaseController
{
    /// <inheritdoc />
    public EnvironmentMetricsController(IMapper mapper) : base(
        mapper)
    {
    }

    /// <summary>
    /// Retrieves names of reports main purpose of which to give general info about the application for particular period
    /// of time. Mostly contain total and average values of some events.
    /// </summary>
    /// <returns><see cref="OkObjectResult"/> with list of reports names.</returns>
    [HttpGet("Schema/[action]")]
    public ActionResult GeneralPeriodicReports()
    {
        var result = new List<string>
        {
            "DailyConnectivity",
            "AggregatedConnectivity",
        };

        return Ok(result);
    }

    /// <summary>
    /// Retrieves daily version of Connectivity report. The report shows the number of users authenticated/registered
    /// grouped by their connection type (wi-fi, cellular, etc.) per day.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyConnectivity"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyConnectivity"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyConnectivity)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyConnectivity> logger,
        [FromServices] IReportService<DailyConnectivity> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves aggregated version of Connectivity report. The report shows the number of users authenticated/registered
    /// grouped by their connection type (wi-fi, cellular, etc.) on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedConnectivity"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedConnectivity"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedConnectivity)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedConnectivity> logger,
        [FromServices] IReportService<AggregatedConnectivity> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }
}