using AutoMapper;
using BlueGuava.Reporting.Common.Models.Entities.ClientReports;
using BlueGuava.Reporting.Common.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OutputCaching;

namespace BlueGuava.Reporting.Api.Controllers;

/// <summary>
/// Contains methods to retrieve reports with platform metrics.
/// </summary>
[ApiVersion("1.0")]
[ApiController]
[Route("api/v{version:apiVersion}/[controller]")]
[OutputCache(PolicyName = "OutputCache")]
public class PlatformMetricsController : BaseController
{
    /// <inheritdoc />
    public PlatformMetricsController(IMapper mapper) : base(
        mapper)
    {
    }

    /// <summary>
    /// Retrieves names of reports main purpose of which to give general info about the application for particular period
    /// of time. Mostly contain total and average values of some events.
    /// </summary>
    /// <returns><see cref="OkObjectResult"/> with list of reports names.</returns>
    [HttpGet("Schema/[action]")]
    public ActionResult GeneralPeriodicReports()
    {
        var result = new List<string>
        {
            "DailyJobType",
            "AggregatedJobType",
            "DailyJobActivities",
            "AggregatedJobActivities",
            "DailyMarker",
            "AggregatedMarker",
            "DailyContentDistribution",
            "AggregatedContentDistribution",
            "DailyContentDuration",
            "AggregatedContentDuration",
            "DailyS3Serialization",
            "AggregatedS3Serialization",
        };

        return Ok(result);
    }

    /// <summary>
    /// Retrieves daily version of JobType report. The report shows total quantity of created jobs grouped by their type on day to day basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyJobType"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyJobType"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyJobType)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyJobType> logger,
        [FromServices] IReportService<DailyJobType> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves aggregated version of JobType report. The report shows total quantity of created jobs grouped by their type on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedJobType"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedJobType"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedJobType)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedJobType> logger,
        [FromServices] IReportService<AggregatedJobType> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves daily version of JobActivities report. The report shows jobs created type, minimum, maximum, and
    /// average job execution time (in seconds) on day to day basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyJobActivities"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyJobActivities"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyJobActivities)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyJobActivities> logger,
        [FromServices] IReportService<DailyJobActivities> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessListDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves aggregated version of JobActivities report. The report shows jobs created type, minimum, maximum, and
    /// average job execution time (in seconds) on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedJobActivities"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedJobActivities"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedJobActivities)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedJobActivities> logger,
        [FromServices] IReportService<AggregatedJobActivities> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessListDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves daily version of Marker report. The report shows the total quantity of created markers grouped by
    /// their type on day to day basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyMarker"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyMarker"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyMarker)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyMarker> logger,
        [FromServices] IReportService<DailyMarker> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves aggregated version of Marker report. The report shows the total quantity of created markers grouped
    /// by their type on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedMarker"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedMarker"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedMarker)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedMarker> logger,
        [FromServices] IReportService<AggregatedMarker> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves daily version of ContentDistribution report. The report shows the number of changes made on entities
    /// from the system - content, customer, reward, etc. grouped by entity type on day to day basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyContentDistribution"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyContentDistribution"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyContentDistribution)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyContentDistribution> logger,
        [FromServices] IReportService<DailyContentDistribution> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves aggregated version of ContentDistribution report. The report the number of changes made on entities
    /// from the system - content, customer, reward, etc. grouped by entity type on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedContentDistribution"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedContentDistribution"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedContentDistribution)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedContentDistribution> logger,
        [FromServices] IReportService<AggregatedContentDistribution> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves daily version of ContentDuration report. The report shows the total number of content created for a
    /// particular duration. Numbers grouped into the following intervals (minutes): less 5, 5-15, 15-30, 30-45,
    /// 45-60, 60-90, 90-120, 120+ on day to day basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyContentDuration"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyContentDuration"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyContentDuration)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyContentDuration> logger,
        [FromServices] IReportService<DailyContentDuration> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves aggregated version of ContentDuration report. The report shows the total number of content created
    /// for a particular duration. Numbers grouped into the following intervals (minutes): less 5, 5-15, 15-30, 30-45,
    /// 45-60, 60-90, 90-120, 120+ on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedContentDuration"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedContentDuration"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedContentDuration)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedContentDuration> logger,
        [FromServices] IReportService<AggregatedContentDuration> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves daily version of S3Serialization report. The report shows serialization type, number of attempts,
    /// and minimum, maximum, and average execution time (in seconds) on day to day basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyS3Serialization"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyS3Serialization"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyS3Serialization)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyS3Serialization> logger,
        [FromServices] IReportService<DailyS3Serialization> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessListDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves aggregated version of S3Serialization report. The report shows serialization type, number of attempts,
    /// and minimum, maximum, and average execution time (in seconds) on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedS3Serialization"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedS3Serialization"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedS3Serialization)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedS3Serialization> logger,
        [FromServices] IReportService<AggregatedS3Serialization> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessListDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }
}