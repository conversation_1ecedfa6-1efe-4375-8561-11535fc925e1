using AutoMapper;
using BlueGuava.Reporting.Common.Models.Entities.ClientReports;
using BlueGuava.Reporting.Common.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OutputCaching;

namespace BlueGuava.Reporting.Api.Controllers;

/// <summary>
/// Contains methods to retrieve reports with user acquisitions metrics.
/// </summary>
[ApiVersion("1.0")]
[ApiController]
[Route("api/v{version:apiVersion}/[controller]")]
[OutputCache(PolicyName = "OutputCache")]
public class UserAcquisitionMetricsController : BaseController
{
    /// <inheritdoc />
    public UserAcquisitionMetricsController(IMapper mapper) : base(
        mapper)
    {
    }

    /// <summary>
    /// Retrieves names of reports calculated for particular invitation code. Reports contain chatId as required parameter.
    /// </summary>
    /// <returns><see cref="OkObjectResult"/> with list of reports names.</returns>
    [HttpGet("Schema/[action]")]
    public ActionResult SingleInvitationCodeReports()
    {
        var result = new List<string>
        {
            "DailyInvitationCodeReturn",
            "AggregatedInvitationCodeReturn"
        };

        return Ok(result);
    }

    /// <summary>
    /// Retrieves daily version of InvitationCodeReturn report. The report shows the total quantity of customers
    /// RegisteredWithInvitationCode (just registered) and SubscriptionWithInvitationCode (subscribed after registration)
    /// using particular invitation code on day to day basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyInvitationCodeReturn"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="invitationCode">Invitation code the report retrieved for.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyInvitationCodeReturn"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyInvitationCodeReturn)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyInvitationCodeReturn> logger,
        [FromServices] IReportService<DailyInvitationCodeReturn> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        [FromQuery] string? invitationCode,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken, invitationCode);
    }

    /// <summary>
    /// Retrieves aggregated version of InvitationCodeReturn report. The report shows the total quantity of customers
    /// RegisteredWithInvitationCode (just registered) and SubscriptionWithInvitationCode (subscribed after registration)
    /// using particular invitation code on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedInvitationCodeReturn"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="invitationCode">Invitation code the report retrieved for.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedInvitationCodeReturn"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedInvitationCodeReturn)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedInvitationCodeReturn> logger,
        [FromServices] IReportService<AggregatedInvitationCodeReturn> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        [FromQuery] string? invitationCode,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken, invitationCode);
    }
}