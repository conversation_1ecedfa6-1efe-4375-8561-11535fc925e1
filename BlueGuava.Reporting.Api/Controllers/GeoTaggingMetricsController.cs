using AutoMapper;
using BlueGuava.Reporting.Common.Models.Entities.ClientReports;
using BlueGuava.Reporting.Common.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OutputCaching;

namespace BlueGuava.Reporting.Api.Controllers;

/// <summary>
/// Contains methods to retrieve reports with geo-tagging metrics.
/// </summary>
[ApiVersion("1.0")]
[ApiController]
[Route("api/v{version:apiVersion}/[controller]")]
[OutputCache(PolicyName = "OutputCache")]
public class GeoTaggingMetricsController : BaseController
{
    /// <inheritdoc />
    public GeoTaggingMetricsController(IMapper mapper) : base(
        mapper)
    {
    }

    /// <summary>
    /// Retrieves names of reports calculated for particular content unit. Reports contain contentId as required parameter.
    /// </summary>
    /// <returns><see cref="OkObjectResult"/> with list of reports names.</returns>
    [HttpGet("Schema/[action]")]
    public ActionResult SingleContentReports()
    {
        var result = new List<string>
        {
            "DailyContentPlaybackLocation",
            "AggregatedContentPlaybackLocation"
        };

        return Ok(result);
    }

    /// <summary>
    /// Retrieves names of reports calculated for particular country. Reports contain countryId as required parameter.
    /// </summary>
    /// <returns><see cref="OkObjectResult"/> with list of reports names.</returns>
    [HttpGet("Schema/[action]")]
    public ActionResult SingleCountryReports()
    {
        var result = new List<string>
        {
            "ContentWeeklyList",
            "CustomerWeeklyList"
        };

        return Ok(result);
    }

    /// <summary>
    /// Retrieves names of reports main purpose of which to give general info about the application for particular period
    /// of time. Mostly contain total and average values of some events.
    /// </summary>
    /// <returns><see cref="OkObjectResult"/> with list of reports names.</returns>
    [HttpGet("Schema/[action]")]
    public ActionResult GeneralPeriodicReports()
    {
        var result = new List<string>
        {
            "DailyChatLocation",
            "AggregatedChatLocation",
            "DailyTotalContentPlaybackLocation",
            "AggregatedTotalContentPlaybackLocation",
            "DailyGeoContentCreation",
            "AggregatedGeoContentCreation",
            "DailyGeoCustomerCreation",
            "AggregatedGeoCustomerCreation",
        };

        return Ok(result);
    }

    /// <summary>
    /// Retrieves daily version of ContentPlaybackLocation report. Report shows the number of playbacks for particular
    /// content grouped by locations of playback on day to day basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyContentPlaybackLocation"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <param name="contentId">Identifier of content the report retrieved for.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyContentPlaybackLocation"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyContentPlaybackLocation)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyContentPlaybackLocation> logger,
        [FromServices] IReportService<DailyContentPlaybackLocation> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        [FromQuery] string contentId,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken, contentId);
    }

    /// <summary>
    /// Retrieves aggregated version of ContentPlaybackLocation report. Report shows the number of playbacks for particular
    /// content grouped by locations of playback on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedContentPlaybackLocation"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <param name="contentId">Identifier of content the report retrieved for.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedContentPlaybackLocation"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedContentPlaybackLocation)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedContentPlaybackLocation> logger,
        [FromServices] IReportService<AggregatedContentPlaybackLocation> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        [FromQuery] string contentId,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken, contentId);
    }

    /// <summary>
    /// Retrieves ContentWeeklyList report. Report shows the top 10 LiveChats by the number of comments left under by
    /// customers in descending order for particular country.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="ContentWeeklyList"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <param name="countryId">The <a href="http://en.wikipedia.org/wiki/ISO_3166-1"> two-character ISO 3166-1 alpha code</a> for the country.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="ContentWeeklyList"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(ContentWeeklyList)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<ContentWeeklyList> logger,
        [FromServices] IReportService<ContentWeeklyList> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        [FromQuery] string? countryId,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken, countryId);
    }

    /// <summary>
    /// Retrieves CustomerWeeklyList report. Report shows shows the top 10 customers with total
    /// comments left in LiveChats during the week for particular country.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="CustomerWeeklyList"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <param name="countryId">The <a href="http://en.wikipedia.org/wiki/ISO_3166-1"> two-character ISO 3166-1 alpha code</a> for the country.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="CustomerWeeklyList"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(CustomerWeeklyList)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<CustomerWeeklyList> logger,
        [FromServices] IReportService<CustomerWeeklyList> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        [FromQuery] string? countryId,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken, countryId);
    }

    /// <summary>
    /// Retrieves daily version of ChatLocation report. The report shows the number of chats created grouped by
    /// locations of its creator on day to day basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyChatLocation"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyChatLocation"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyChatLocation)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyChatLocation> logger,
        [FromServices] IReportService<DailyChatLocation> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves aggregated version of ChatLocation report. The report shows the number of chats created grouped by
    /// locations of its creator on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedChatLocation"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedChatLocation"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedChatLocation)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedChatLocation> logger,
        [FromServices] IReportService<AggregatedChatLocation> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves daily version of TotalContentLocation report. Report shows the number of playbacks for all
    /// content grouped by locations of playback on day to day basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyTotalContentPlaybackLocation"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyTotalContentPlaybackLocation"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyTotalContentPlaybackLocation)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyTotalContentPlaybackLocation> logger,
        [FromServices] IReportService<DailyTotalContentPlaybackLocation> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves aggregated version of TotalContentLocation report. Report shows the number of playbacks for all content
    /// grouped by locations of playback on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedTotalContentPlaybackLocation"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedTotalContentPlaybackLocation"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedTotalContentPlaybackLocation)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedTotalContentPlaybackLocation> logger,
        [FromServices] IReportService<AggregatedTotalContentPlaybackLocation> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves daily version of GeoContentCreation report. The report shows the amount of content created and grouped by
    /// locations of its creator on day to day basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyGeoContentCreation"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyGeoContentCreation"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyGeoContentCreation)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyGeoContentCreation> logger,
        [FromServices] IReportService<DailyGeoContentCreation> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves aggregated version of GeoContentCreation report. The report shows the amount of content created and grouped by
    /// locations of its creator on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedGeoContentCreation"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedGeoContentCreation"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedGeoContentCreation)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedGeoContentCreation> logger,
        [FromServices] IReportService<AggregatedGeoContentCreation> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves daily version of GeoCustomerCreation report. The report shows the number of customers created and
    /// grouped by their location on day to day basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyGeoCustomerCreation"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyGeoCustomerCreation"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyGeoCustomerCreation)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyGeoCustomerCreation> logger,
        [FromServices] IReportService<DailyGeoCustomerCreation> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves aggregated version of GeoCustomerCreation report. The report shows the number of customers created and
    /// grouped by their location on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedGeoCustomerCreation"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedGeoCustomerCreation"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedGeoCustomerCreation)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedGeoCustomerCreation> logger,
        [FromServices] IReportService<AggregatedGeoCustomerCreation> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }
}