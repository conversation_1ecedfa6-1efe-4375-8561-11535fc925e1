using AutoMapper;
using BlueGuava.Reporting.Common.Models.Entities.ClientReports;
using BlueGuava.Reporting.Common.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OutputCaching;

namespace BlueGuava.Reporting.Api.Controllers;

/// <summary>
/// Contains methods to retrieve reports with activity metrics.
/// </summary>
[ApiVersion("1.0")]
[ApiController]
[Route("api/v{version:apiVersion}/[controller]")]
[OutputCache(PolicyName = "OutputCache")]
public class ActivityMetricsController : BaseController
{
    /// <inheritdoc />
    public ActivityMetricsController(IMapper mapper) : base(
        mapper)
    {
    }

    /// <summary>
    /// Retrieves names of reports main purpose of which to give general info about the application for particular period
    /// of time. Mostly contain total and average values of some events.
    /// </summary>
    /// <returns><see cref="OkObjectResult"/> with list of reports names.</returns>
    [HttpGet("Schema/[action]")]
    public ActionResult GeneralPeriodicReports()
    {
        var result = new List<string>
        {
            "DailyContentRating",
            "AggregatedContentRating",
            "DailyGeneralContentRelationship",
            "AggregatedGeneralContentRelationship",
            "DailyGeneralCustomerRelationship",
            "AggregatedGeneralCustomerRelationship",
            "DailyActiveHoursByHour",
            "DailyActiveHoursByDay",
            "DailySuperChatMessageType",
            "AggregatedSuperChatMessageType"
        };

        return Ok(result);
    }

    /// <summary>
    /// Retrieves names of reports calculated for particular content unit. Reports contain contentId as required parameter.
    /// </summary>
    /// <returns><see cref="OkObjectResult"/> with list of reports names.</returns>
    [HttpGet("Schema/[action]")]
    public ActionResult SingleContentReports()
    {
        var result = new List<string>
        {
            "ConcurrentVisitorsCounter"
        };

        return Ok(result);
    }

    /// <summary>
    /// Retrieves daily version of ContentRating report. Report reflects total quantity of rating action
    /// made by user and average value of that action from 0 to 10 on day to day basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyContentRating"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyContentRating"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyContentRating)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyContentRating> logger,
        [FromServices] IReportService<DailyContentRating> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves aggregated version of ContentRating report. Report reflects total quantity of rating action
    /// made by user and average value of that action from 0 to 10 on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedContentRating"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedContentRating"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedContentRating)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedContentRating> logger,
        [FromServices] IReportService<AggregatedContentRating> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves daily version of GeneralContentRelationship report. Report reflects how many
    /// relationship with RelationString value set to 'Content' created and deleted per day.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyGeneralContentRelationship"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyGeneralContentRelationship"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyGeneralContentRelationship)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyGeneralContentRelationship> logger,
        [FromServices] IReportService<DailyGeneralContentRelationship> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves aggregated version of GeneralContentRelationship report. Report reflects how many
    /// relationship with RelationString value set to 'Content' created and deleted on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedGeneralContentRelationship"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedGeneralContentRelationship"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedGeneralContentRelationship)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedGeneralContentRelationship> logger,
        [FromServices] IReportService<AggregatedGeneralContentRelationship> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves daily version of GeneralCustomerRelationship report. Report reflects how many
    /// relationship with RelationString value set to 'Customer' created and deleted per day.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyGeneralCustomerRelationship"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyGeneralCustomerRelationship"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyGeneralCustomerRelationship)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyGeneralCustomerRelationship> logger,
        [FromServices] IReportService<DailyGeneralCustomerRelationship> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves aggregated version of GeneralCustomerRelationship report. Report reflects how many
    /// relationship with RelationString value set to 'Customer' created and deleted on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedGeneralCustomerRelationship"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedGeneralCustomerRelationship"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedGeneralCustomerRelationship)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedGeneralCustomerRelationship> logger,
        [FromServices] IReportService<AggregatedGeneralCustomerRelationship> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves DailyActiveHoursByHour. The report shows the number of logged-in users, total watch minutes of content,
    /// and the total number of chat messages per hour during the day.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyActiveHoursByHour"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyActiveHoursByHour"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyActiveHoursByHour)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyActiveHoursByHour> logger,
        [FromServices] IReportService<DailyActiveHoursByHour> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessListDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves DailyActiveHoursByHour. The report shows the number of logged-in users, total watch minutes of content,
    /// and the total number of chat messages per day.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailyActiveHoursByDay"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailyActiveHoursByDay"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailyActiveHoursByDay)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailyActiveHoursByDay> logger,
        [FromServices] IReportService<DailyActiveHoursByDay> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves ConcurrentVisitorsCounter report. The report shows the maximum number of visitors of particular event
    /// distributed by hours the event was held. To be shown, event's counter should change in any direction between
    /// <paramref name="from"/> and <paramref name="to"/> date parameters.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="ConcurrentVisitorsCounter"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="contentId">Identifier of content the report retrieved for.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="ConcurrentVisitorsCounter"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(ConcurrentVisitorsCounter)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<ConcurrentVisitorsCounter> logger,
        [FromServices] IReportService<ConcurrentVisitorsCounter> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        [FromQuery] string contentId,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken, contentId);
    }






    /// <summary>
    /// Retrieves daily version of SuperChatMessageType report. Report shows the number of messages left in SuperChats
    /// grouped by message type per day.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="DailySuperChatMessageType"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="DailySuperChatMessageType"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(DailySuperChatMessageType)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<DailySuperChatMessageType> logger,
        [FromServices] IReportService<DailySuperChatMessageType> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }

    /// <summary>
    /// Retrieves aggregated version of SuperChatMessageType report. Report shows the number of messages left in SuperChats
    /// grouped by message type on month to date basis.
    /// </summary>
    /// <param name="logger">Logger</param>
    /// <param name="reportService">Reporting service responsible for processing report of type: <see cref="AggregatedSuperChatMessageType"/>.</param>
    /// <param name="from">Start of the time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="to">End of time period the report should be returned for. Should be in "yyyy-MM-dd" format.</param>
    /// <param name="cancellationToken">CancellationToken.</param>
    /// <returns><see cref="OkObjectResult"/> with <see cref="AggregatedSuperChatMessageType"/> report in case of successful response.
    /// Otherwise <see cref="BadRequestObjectResult"/> with error message.</returns>
    [GetReportRoute($"{nameof(AggregatedSuperChatMessageType)}")]
    public async Task<ActionResult> Action(
        [FromServices] ILogger<AggregatedSuperChatMessageType> logger,
        [FromServices] IReportService<AggregatedSuperChatMessageType> reportService,
        [FromQuery] DateTime? from,
        [FromQuery] DateTime? to,
        CancellationToken cancellationToken)
    {
        return await ProcessDictionaryBaseReport(logger, reportService, from, to, cancellationToken);
    }
}