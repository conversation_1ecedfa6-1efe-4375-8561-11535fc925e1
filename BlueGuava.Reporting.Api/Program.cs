using System.IO.Compression;
using System.Reflection;
using Amazon.DynamoDBv2.DataModel;
using Amazon.S3;
using Amazon.ECS;
using Amazon.Scheduler;
using Amazon.SecurityToken.Model;
using Amazon.SecurityToken;
using BlueGuava.Extensions.AspNetCore.Authentication;
using BlueGuava.Extensions.AspNetCore.ExceptionHandling;
using BlueGuava.Extensions.AWS.MessageQueuing.Amazon.DependencyInjection;
using BlueGuava.Extensions.Configuration;
using BlueGuava.Extensions.Configuration.Models;
using BlueGuava.Extensions.Telemetry;
using BlueGuava.Reporting.Api.Caching;
using BlueGuava.Reporting.Api.Extensions;
using BlueGuava.Reporting.Api.Models;
using BlueGuava.Reporting.Api.Monitoring;
using BlueGuava.Reporting.Api.Queuing;
using BlueGuava.Reporting.Common;
using BlueGuava.Reporting.Common.Models;
using BlueGuava.Reporting.Common.Models.Entities.EventBridge;
using BlueGuava.Reporting.Common.Monitoring;
using BlueGuava.Reporting.Common.Repositories;
using BlueGuava.Reporting.Common.Services;
using BlueGuava.Reporting.Messages;
using BlueGuava.Reporting.Messages.Entities;
using BlueGuava.Reporting.Repository.DynamoDb;
using BlueGuava.Reporting.Repository.S3;
using BlueGuava.Reporting.Services;
using BlueGuava.Reporting.Services.ItemProcessors;
using CorrelationId;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using Prometheus;
using Microsoft.FeatureManagement;
using Prometheus.HttpMetrics;
using BlueGuava.JobManagement.Common.Settings;
using BlueGuava.Extensions.Swagger.ApiVersioning;
using Microsoft.AspNetCore.Mvc.Versioning;
using Microsoft.AspNetCore.Mvc.ApiExplorer;

const string CorsPolicy = "CorsPolicy";
var builder = WebApplication.CreateBuilder(args);

builder.Configuration.AddSystemsManager("/bg-jm/");
builder.Configuration.AddSystemsManager("/ENT/");
builder.Configuration.AddParameterStore("BlueGuava:Reporting:Api");
builder.Configuration.AddVersionConfiguration();
builder.Host.ConfigureSerilog();

ConfigurationManager configuration = builder.Configuration;

// Add services to the container.
var services = builder.Services;


services.AddControllers();
services.AddCorrelationId();
services.AddApiVersioning(options =>
{
    //options.UseApiBehavior = false,
    options.DefaultApiVersion = new Microsoft.AspNetCore.Mvc.ApiVersion(1, 0);
    options.AssumeDefaultVersionWhenUnspecified = true;
    options.ReportApiVersions = true;
    options.ApiVersionReader = ApiVersionReader.Combine(new UrlSegmentApiVersionReader(),
                                                    new HeaderApiVersionReader("x-api-version"),
                                                    new MediaTypeApiVersionReader("x-api-version"));
});
services.AddVersionedApiExplorer(setup =>
{
    setup.GroupNameFormat = "'v'VVV";
    setup.SubstituteApiVersionInUrl = true;
});
services.AddHttpContextAccessor();
services.AddEndpointsApiExplorer();

services.AddMvc(o =>
{
    o.Filters.Add(new ProducesResponseTypeAttribute(typeof(ResponseEntity), 200));
    o.Filters.Add(new ProducesResponseTypeAttribute(typeof(string), 400));
});

services.AddCors(options =>
{
    options.AddPolicy(CorsPolicy,
        builder => builder
            .AllowAnyOrigin()
            .AllowAnyMethod()
            .AllowAnyHeader());
});


services.AddSwaggerGen(opts =>
{
    opts.SwaggerDoc("v1.0", new OpenApiInfo { Title = "ENT.360 - Reporting Service", Version = "v1.0" });
    opts.AddBearerTokenRequirement();
    opts.CustomSchemaIds(x => x.FullName);
    opts.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, $"{Assembly.GetExecutingAssembly().GetName().Name}.xml"));
});
builder.Services.AddVersioning();

services.Configure<ReportProcessorFargateTaskSettings>(configuration.GetSection("ReportProcessorFargateTaskSettings"));
services.Configure<ContentTokenOptions>(configuration.GetSection("ContentToken"));
services.Configure<CommonFargateTaskSettings>(configuration.GetSection("CommonFargateTaskSettings"));
services.Configure<BlueGuava.Extensions.Configuration.Models.ApiVersion>(configuration.GetSection("ApiVersion"));

services.Configure<ModuleInfo>(configuration.GetSection("ModuleInfo"));
services.AddDefaultAWSOptions(configuration.GetAWSOptions());

services.AddAmazonSQS(configuration)
    .AddAmazonMessageQueue<ReportMessage>("ReportMessages")
        .ConfigureSerializer(settings => settings.TypeNameHandling = TypeNameHandling.All)
        .AddMessageQueueWorker(configuration).ForItemProcessor<ReportMessageProcessor>()
    .AddAmazonMessageQueue<CommercialMetricMessage>("CommercialMetricMessages")
        .ConfigureSerializer(settings => settings.TypeNameHandling = TypeNameHandling.All)
        .AddMessageQueueWorker(configuration).ForItemProcessor<CommercialMetricMessageProcessor>()
    .AddAmazonMessageQueue<PlatformMetricMessage>("PlatformMetricMessages")
        .ConfigureSerializer(settings => settings.TypeNameHandling = TypeNameHandling.All)
        .AddMessageQueueWorker(configuration).ForItemProcessor<PlatformMetricMessageProcessor>()
    .AddAmazonMessageQueue<CustomerMetricMessage>("CustomerMetricMessages")
        .ConfigureSerializer(settings => settings.TypeNameHandling = TypeNameHandling.All)
        .AddMessageQueueWorker(configuration).ForItemProcessor<CustomerMetricMessageProcessor>()
    .AddAmazonMessageQueue<ContentCreationMetricMessage>("ContentCreationMetricMessages")
        .ConfigureSerializer(settings => settings.TypeNameHandling = TypeNameHandling.All)
        .AddMessageQueueWorker(configuration).ForItemProcessor<ContentCreationMetricMessageProcessor>()
    .AddAmazonMessageQueue<EnvironmentMetricMessage>("EnvironmentMetricMessages")
        .ConfigureSerializer(settings => settings.TypeNameHandling = TypeNameHandling.All)
        .AddMessageQueueWorker(configuration).ForItemProcessor<EnvironmentMetricMessageProcessor>()
    .AddAmazonMessageQueue<ClientReportMessage>("ClientReportMessages")
        .AddMessageQueueWorker(configuration).ForItemProcessor<ConsumptionProcessor>()
    .AddAWSService<IAmazonS3>() // setup credentials
    .AddAWSService<Amazon.DynamoDBv2.IAmazonDynamoDB>()
    .AddTransient<IDynamoDBContext, DynamoDBContext>()
    .AddAWSService<IAmazonScheduler>()
    .AddAWSService<IAmazonECS>();

services.AddSingleton<IS3Repository, S3Repository>();

services.AddReportRepositories();

services.AddTransient(typeof(IReportService<>), typeof(ReportService<>));

services.AddScoped<IReportManagingService, ReportManagingService>();
services.AddScoped<IReportSetupRepository, ReportSetupRepository>();

services.AddSingleton<IMetricsCollector, PrometheusMetrics>();
services.AddHostedService<MetricsInitializer>();

//adds reporting item processor
services.AddDataProducersAndChannelItemProcessors(configuration);

services.AddItemsChannel<InvalidationTrigger>(configuration.GetSection("Invalidation"))
     .AddItemProcessor<TriggerProcessor>();

services.Configure<BlueGuava.JwtToken.JwtSettings>(configuration.GetSection("jwt"));
services.AddScoped<BlueGuava.JwtToken.IJwtTokenValidator, BlueGuava.JwtToken.JwtTokenValidator>();
services
    .AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddBlueGuavaJwtBearer(configuration);

services.AddFeatureManagement();

services.AddResponseCompression(options =>
{
    options.EnableForHttps = true;
    options.Providers.Add<BrotliCompressionProvider>();
    options.Providers.Add<GzipCompressionProvider>();
    options.MimeTypes = ResponseCompressionDefaults.MimeTypes.Concat(new[] { "image/svg+xml" });
});

services.Configure<BrotliCompressionProviderOptions>(options =>
{
    options.Level = CompressionLevel.Fastest;
});
services.Configure<GzipCompressionProviderOptions>(options =>
{
    options.Level = CompressionLevel.SmallestSize;
});

builder.Services.AddAutoMapper(AppDomain.CurrentDomain.GetAssemblies());
builder.Services.AddOutputCache(options =>
{
    options.AddPolicy("OutputCachePolicy", cachePolicyBuilder => cachePolicyBuilder.AddPolicy<OutputCachePolicy>(), true);
    //options.AddBasePolicy(policyBuilder => policyBuilder.AddPolicy<OutputCachePolicy>(), true);
});

var app = builder.Build();
var provider = app.Services.GetRequiredService<IApiVersionDescriptionProvider>();


// retrieve the logger
var logger = app.Services.GetService<ILogger<Program>>();
if (app.Services.GetService<IWebHostEnvironment>()?.EnvironmentName != "Production")
{
    var config = app.Services.GetRequiredService<IConfiguration>();
    var stsClient = config.GetAWSOptions().CreateServiceClient<IAmazonSecurityTokenService>();
    var identity = stsClient.GetCallerIdentityAsync(new GetCallerIdentityRequest { }).Result;
    logger?.LogInformation("AWS Account={Account} ARN={Arn} UserId={UserId}", identity.Account, identity.Arn, identity.UserId);
}

app.UseResponseCompression();
app.UseCors(CorsPolicy);
            // Add security headers
            app.Use(async (context, next) =>
            {
                context.Response.Headers.Add("X-SV", Configuration["ModuleInfo:Version"]);

                // Content Security Policy - Helps prevent XSS attacks
                context.Response.Headers.Add("Content-Security-Policy",
                    "default-src 'self'; " +
                    "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
                    "style-src 'self' 'unsafe-inline'; " +
                    "img-src 'self' data: https:; " +
                    "font-src 'self' data:; " +
                    "connect-src 'self'; " +
                    "frame-ancestors 'none'");

                // X-Frame-Options - Prevents clickjacking attacks
                context.Response.Headers.Add("X-Frame-Options", "DENY");

                // Referrer Policy - Controls referrer information
                context.Response.Headers.Add("Referrer-Policy", "strict-origin-when-cross-origin");

                // Permissions Policy - Controls browser features
                context.Response.Headers.Add("Permissions-Policy",
                    "camera=(), " +
                    "microphone=(), " +
                    "geolocation=(), " +
                    "payment=(), " +
                    "usb=(), " +
                    "magnetometer=(), " +
                    "gyroscope=(), " +
                    "accelerometer=()");

                // Strict Transport Security - Enforces HTTPS (only add in production)
                var env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
                if (!string.IsNullOrEmpty(env) && env == Environments.Production)
                {
                    context.Response.Headers.Add("Strict-Transport-Security",
                        "max-age=31536000; includeSubDomains; preload");
                }

                // Additional security headers
                context.Response.Headers.Add("X-Content-Type-Options", "nosniff");
                context.Response.Headers.Add("X-XSS-Protection", "1; mode=block");

                await next();
            });

var featureManager = app.Services.GetService<IFeatureManager>();
if (featureManager != null && featureManager.IsEnabledAsync("AllowSwagger").Result)
{
    app.UseSwagger();
    app.UseVersionedSwaggerUI(provider);
}

app.UseConfigRenewal("/Reload");

//Prometheus Monitoring
app.UseMetricServer();
app.UseHttpMetrics(opts =>
{
    opts.AddRouteParameter(new HttpRouteParameterMapping("version", "apiVersion"));
});

app.UseCorrelationId();
app.UseAuthorization();
app.UseAuthentication();
app.UseExceptionMiddleware();

app.UseOutputCache();

app.MapControllers();

app.Run();
