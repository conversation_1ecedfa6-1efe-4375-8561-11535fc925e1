using AutoMapper;

namespace BlueGuava.Reporting.Api.Models.Profiles;

public class ReportMappingProfile : Profile
{
    public ReportMappingProfile()
    {
        CreateMap<Common.Models.Entities.ClientReports.DailyContentByCreatorsConsumption, Item>()
            .AfterMap((src, dest) => dest.Id = src.EntityId!);

        CreateMap<Common.Models.Entities.ClientReports.DailyContentConsumption, Item>()
            .AfterMap((src, dest) => dest.Id = src.EntityId!);

        CreateMap<Common.Models.Entities.ClientReports.DailyCustomerConsumption, Item>()
            .AfterMap((src, dest) => dest.Id = src.EntityId!);

        CreateMap<Common.Models.Entities.ClientReports.DailyTotalWatchMinutes, Item>()
            .AfterMap((src, dest) => dest.Id = src.EntityId!);

        CreateMap<Common.Models.Entities.ClientReports.DailyContentConsumptionHeatMap, Item>()
            .AfterMap((src, dest) => dest.Id = src.EntityId!)
            .AfterMap((src, dest) => dest.Quantity = src.HeatMap!);

        CreateMap<Common.Models.Entities.ClientReports.DailyBestContentWatched, Item>()
            .AfterMap((src, dest) => dest.Id = "DailyBestContents")
            .AfterMap((src, dest) => dest.Quantity = src.Quantities!);
        CreateMap<Common.Models.Entities.ClientReports.AggregatedBestContentWatched, Item>()
            .AfterMap((src, dest) => dest.Id = "MonthlyBestContents")
            .AfterMap((src, dest) => dest.Quantity = src.Quantities!);

        CreateMap<Common.Models.Entities.ClientReports.DailyWorstContentWatched, Item>()
            .AfterMap((src, dest) => dest.Id = "DailyWorstContents")
            .AfterMap((src, dest) => dest.Quantity = src.Quantities!);
        CreateMap<Common.Models.Entities.ClientReports.AggregatedWorstContentWatched, Item>()
            .AfterMap((src, dest) => dest.Id = "MonthlyWorstContents")
            .AfterMap((src, dest) => dest.Quantity = src.Quantities!);


        CreateMap<Common.Models.Entities.ClientReports.DailyTotalUsers, Item>()
            .AfterMap((src, dest) => dest.Id = "DailyTotalUsers")
            .AfterMap((src, dest) => dest.Quantity = src.Quantity);

        CreateMap<Common.Models.Entities.ClientReports.AggregatedTotalUsers, Item>()
            .AfterMap((src, dest) => dest.Id = "TotalUsers")
            .AfterMap((src, dest) => dest.Quantity = src.Quantity);

        CreateMap<Common.Models.Entities.ClientReports.DailyTotalContentWatched, Item>()
            .AfterMap((src, dest) => dest.Id = "DailyTotalContentWatched")
            .AfterMap((src, dest) => dest.Quantity = src.Quantity);

        CreateMap<Common.Models.Entities.ClientReports.AggregatedTotalContentWatched, Item>()
            .AfterMap((src, dest) => dest.Id = "TotalContentWatched")
            .AfterMap((src, dest) => dest.Quantity = src.Quantity);

        CreateMap<Common.Models.Entities.ClientReports.DailyTotalContentWatchedP50, Item>()
            .AfterMap((src, dest) => dest.Id = "DailyTotalContentWatchedP50")
            .AfterMap((src, dest) => dest.Quantity = src.Quantity);

        CreateMap<Common.Models.Entities.ClientReports.DailyTotalContentWatchedP90, Item>()
            .AfterMap((src, dest) => dest.Id = "DailyTotalContentWatchedP90")
            .AfterMap((src, dest) => dest.Quantity = src.Quantity);


        CreateMap<Common.Models.Entities.ClientReports.DailyTargetNumberOfFavourites, Item>()
            .AfterMap((src, dest) => dest.Id = "DailyTargetNumberOfFavourites")
            .AfterMap((src, dest) => dest.Quantity = src.Quantity);

        CreateMap<Common.Models.Entities.ClientReports.AggregatedTargetNumberOfFavourites, Item>()
            .AfterMap((src, dest) => dest.Id = "DailyTargetNumberOfFavourites")
            .AfterMap((src, dest) => dest.Quantity = src.Quantity);


        CreateMap<Common.Models.Entities.ClientReports.DailyCreatorsMembers, Item>()
            .AfterMap((src, dest) => dest.Id = "DailyCreatorsMembers")
            .AfterMap((src, dest) => dest.Quantity = src.Quantity);

        CreateMap<Common.Models.Entities.ClientReports.AggregatedCreatorsMembers, Item>()
            .AfterMap((src, dest) => dest.Id = "AggregatedCreatorsMembers")
            .AfterMap((src, dest) => dest.Quantity = src.Quantity);

        CreateMap<Common.Models.Entities.ClientReports.DailyEventsMembers, Item>()
            .AfterMap((src, dest) => dest.Id = "DailyEventsMembers")
            .AfterMap((src, dest) => dest.Quantity = src.Quantity);

        CreateMap<Common.Models.Entities.ClientReports.AggregatedEventsMembers, Item>()
            .AfterMap((src, dest) => dest.Id = "AggregatedEventsMembers")
            .AfterMap((src, dest) => dest.Quantity = src.Quantity);

        CreateMap<Common.Models.Entities.ClientReports.DailyOrganisationsMembers, Item>()
            .AfterMap((src, dest) => dest.Id = "DailyOrganisationsMembers")
            .AfterMap((src, dest) => dest.Quantity = src.Quantity);

        CreateMap<Common.Models.Entities.ClientReports.AggregatedOrganisationsMembers, Item>()
            .AfterMap((src, dest) => dest.Id = "AggregatedOrganisationsMembers")
            .AfterMap((src, dest) => dest.Quantity = src.Quantity);

        CreateMap<Common.Models.Entities.ClientReports.DailyProjectsMembers, Item>()
            .AfterMap((src, dest) => dest.Id = "DailyProjectsMembers")
            .AfterMap((src, dest) => dest.Quantity = src.Quantity);

        CreateMap<Common.Models.Entities.ClientReports.AggregatedProjectsMembers, Item>()
            .AfterMap((src, dest) => dest.Id = "AggregatedProjectsMembers")
            .AfterMap((src, dest) => dest.Quantity = src.Quantity);

        CreateMap<Common.Models.Entities.ClientReports.DailySponsorsMembers, Item>()
            .AfterMap((src, dest) => dest.Id = "DailySponsorsMembers")
            .AfterMap((src, dest) => dest.Quantity = src.Quantity);

        CreateMap<Common.Models.Entities.ClientReports.AggregatedSponsorsMembers, Item>()
            .AfterMap((src, dest) => dest.Id = "AggregatedSponsorsMembers")
            .AfterMap((src, dest) => dest.Quantity = src.Quantity);

        CreateMap<Common.Models.Entities.ClientReports.DailyNumberOfContentAccess, Item>()
            .AfterMap((src, dest) => dest.Id = src.EntityId)
            .AfterMap((src, dest) => dest.Quantity = src.Quantity);

        CreateMap<Common.Models.Entities.ClientReports.DailyNumberOfContentAccess, Item>()
            .AfterMap((src, dest) => dest.Id = src.EntityId);

        CreateMap<Common.Models.Entities.ClientReports.AggregatedNumberOfContentAccess, Item>()
            .AfterMap((src, dest) => dest.Id = src.EntityId);

        CreateMap<Common.Models.Entities.ClientReports.DailyContentQualityTime, Item>()
            .AfterMap((src, dest) => dest.Id = src.EntityId);

        CreateMap<Common.Models.Entities.ClientReports.AggregatedContentQualityTime, Item>()
            .AfterMap((src, dest) => dest.Id = src.EntityId);

        CreateMap<Common.Models.Entities.ClientReports.DailyLoginPlayback, Item>()
            .AfterMap((src, dest) => dest.Id = "DailyLoginPlayback")
            .AfterMap((src, dest) => dest.Quantity = src.Quantities!);
        CreateMap<Common.Models.Entities.ClientReports.AggregatedLoginPlayback, Item>()
            .AfterMap((src, dest) => dest.Id = "AggregatedLoginPlayback")
            .AfterMap((src, dest) => dest.Quantity = src.Quantities!);

        CreateMap<Common.Models.Entities.ClientReports.DailyTotalContentTimeSpent, Item>()
            .AfterMap((src, dest) => dest.Id = "DailyTotalContentTimeSpent")
            .AfterMap((src, dest) => dest.Quantity = src.Quantity);

        CreateMap<Common.Models.Entities.ClientReports.AggregatedTotalContentTimeSpent, Item>()
            .AfterMap((src, dest) => dest.Id = "AggregatedTotalContentTimeSpent")
            .AfterMap((src, dest) => dest.Quantity = src.Quantity);

        CreateMap<Common.Models.Entities.ClientReports.DailyJobActivities, Item>()
            .AfterMap((src, dest) => dest.Id = "DailyJobActivities")
            .AfterMap((src, dest) => dest.Quantity = src.Quantities!);
        CreateMap<Common.Models.Entities.ClientReports.AggregatedJobActivities, Item>()
            .AfterMap((src, dest) => dest.Id = "AggregatedJobActivities")
            .AfterMap((src, dest) => dest.Quantity = src.Quantities!);

        CreateMap<Common.Models.Entities.ClientReports.DailyS3Serialization, Item>()
            .AfterMap((src, dest) => dest.Id = "DailyS3Serialization")
            .AfterMap((src, dest) => dest.Quantity = src.Quantities!);
        CreateMap<Common.Models.Entities.ClientReports.AggregatedS3Serialization, Item>()
            .AfterMap((src, dest) => dest.Id = "AggregatedS3Serialization")
            .AfterMap((src, dest) => dest.Quantity = src.Quantities!);

        CreateMap<Common.Models.Entities.ClientReports.DailyChatActivity, Item>()
            .AfterMap((src, dest) => dest.Id = "DailyChatActivity");
        CreateMap<Common.Models.Entities.ClientReports.AggregatedChatActivity, Item>()
            .AfterMap((src, dest) => dest.Id = "AggregatedChatActivity");

        CreateMap<Common.Models.Entities.InternalReports.DailyCatalogDeleted, Item>()
            .AfterMap((src, dest) => dest.Id = "DailyCatalogDeleted")
            .AfterMap((src, dest) => dest.Quantity = src.Quantities!);
        CreateMap<Common.Models.Entities.InternalReports.DailyContentDeleted, Item>()
            .AfterMap((src, dest) => dest.Id = "DailyContentDeleted")
            .AfterMap((src, dest) => dest.Quantity = src.Quantities!);
        CreateMap<Common.Models.Entities.InternalReports.DailyCustomerDeleted, Item>()
            .AfterMap((src, dest) => dest.Id = "DailyCustomerDeleted")
            .AfterMap((src, dest) => dest.Quantity = src.Quantities!);

        CreateMap<Common.Models.Entities.ClientReports.DailyActiveHoursByHour, Item>()
            .AfterMap((src, dest) => dest.Id = "DailyActiveHoursByHour")
            .AfterMap((src, dest) => dest.Quantity = src.Quantities!);
    }
}