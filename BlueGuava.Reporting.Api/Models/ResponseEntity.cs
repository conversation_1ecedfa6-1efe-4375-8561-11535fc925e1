namespace BlueGuava.Reporting.Api.Models;

public class ResponseEntity
{
    public List<string>? Metrics { get; set; }

    public IEnumerable<DataItem>? DataItems { get; set; }
}

public class DataItem
{
    public string? Date { get; set; }

    public List<Item>? Data { get; set; }
}

public class Item
{
    public string Id { get; set; } = null!;
    public object Quantity { get; set; } = null!;
}