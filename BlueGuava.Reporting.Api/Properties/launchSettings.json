{"profiles": {"Brand-Dev": {"commandName": "Project", "launchBrowser": true, "applicationUrl": "https://localhost:51737;http://localhost:51738", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Nexius-Dev": {"commandName": "Project", "launchBrowser": true, "applicationUrl": "https://localhost:51737;http://localhost:51738", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Nexius_Dev"}}, "Imagi-Stg": {"commandName": "Project", "launchBrowser": true, "applicationUrl": "https://localhost:51737;http://localhost:51738", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Stage_Imagi"}}, "Nexius-Stg": {"commandName": "Project", "launchBrowser": true, "applicationUrl": "https://localhost:51737;http://localhost:51738", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Stage_Nexius"}}, "Nexius-Qa": {"commandName": "Project", "launchBrowser": true, "applicationUrl": "https://localhost:51737;http://localhost:51738", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Qa_Nexius"}}}}