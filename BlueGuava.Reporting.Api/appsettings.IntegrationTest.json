{"Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Error", "System": "Error", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.AspNetCore.Hosting.Diagnostics": "Error", "AspNetCore.HealthChecks.UI": "Warning", "HealthChecks": "Warning", "Microsoft.AspNetCore.Authentication": "Information"}}, "Enrich": ["FromLogContext", "With", "WithExceptionDetails", "WithCorrelationId"], "Using": ["Serilog.Sinks.Console"], "WriteTo": {"0": {"Name": "<PERSON><PERSON><PERSON>", "Args": {"theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console", "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} <s:{SourceContext}>{NewLine}{Exception}", "type": "Serilog.Formatting.Json.Json<PERSON>, Ser<PERSON>g", "renderMessage": true}}}}, "Athena": {"Database": "reporting", "OutputLocation": "s3://ent-reporting-out-dev-brand-us-east-1/Content"}, "AWS": {"Region": "us-east-1", "Profile": "dev-testbrand"}}