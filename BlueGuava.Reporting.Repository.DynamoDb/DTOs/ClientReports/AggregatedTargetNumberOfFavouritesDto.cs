using Amazon.DynamoDBv2.DataModel;

namespace BlueGuava.Reporting.Repository.DynamoDb.DTOs.ClientReports;

public class AggregatedTargetNumberOfFavouritesDto : ReportDto
{
    public AggregatedTargetNumberOfFavouritesDto() : base("AggregatedTargetNumberOfFavourites") { }

    [DynamoDBRangeKey]
    public override string RunDateId { get => $"{Date}#{Id}"; [Obsolete("Don't delete, used for TypeAndDate mapping")] set => _ = value; }

    public string? EntityId { get; set; }

    public long Quantity { get; set; }
}
