using Amazon.DynamoDBv2.DataModel;

namespace BlueGuava.Reporting.Repository.DynamoDb.DTOs.ClientReports;

public class AggregatedMarkerDto : ReportDto
{
    public AggregatedMarkerDto() : base("AggregatedMarker") { }

    [DynamoDBRangeKey]
    public override string RunDateId { get => $"{Date}"; [Obsolete("Don't delete, used for TypeAndDate mapping")] set => _ = value; }

    public string? Quantities { get; set; }
}
