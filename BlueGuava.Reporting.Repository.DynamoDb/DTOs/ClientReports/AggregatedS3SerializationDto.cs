using Amazon.DynamoDBv2.DataModel;

namespace BlueGuava.Reporting.Repository.DynamoDb.DTOs.ClientReports;

public class AggregatedS3SerializationDto : ReportDto
{
    public AggregatedS3SerializationDto() : base("AggregatedS3Serialization") { }

    [DynamoDBRangeKey]
    public override string RunDateId { get => $"{Date}"; [Obsolete("Don't delete, used for TypeAndDate mapping")] set => _ = value; }

    public string? Quantities { get; set; }
}
