using BlueGuava.EventStream.Common.Model.Classification.Relationship;
using BlueGuava.Extensions.AWS.EventStream.Producer;
using BlueGuava.Reporting.Common.Monitoring;
using Microsoft.Extensions.Logging;

namespace BlueGuava.Reporting.Services.ItemProcessors;

public class RelationshipItemProcessor : GeneralItemProcessor<DataContract>
{
    protected override string ReportMessageName { get; set; } = "RelationshipContract";

    public RelationshipItemProcessor(
        ILogger<RelationshipItemProcessor> logger,
        IDataProducer<DataContract> dataProducer,
        IMetricsCollector metricsCollector) : base(logger, dataProducer, metricsCollector)
    {
        Name = nameof(RelationshipItemProcessor);
    }
}