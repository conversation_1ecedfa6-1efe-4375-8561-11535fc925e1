using BlueGuava.EventStream.Common.Model.Classification.RewardHistory;
using BlueGuava.Extensions.AWS.EventStream.Producer;
using BlueGuava.Reporting.Common.Monitoring;
using Microsoft.Extensions.Logging;

namespace BlueGuava.Reporting.Services.ItemProcessors;

public class RewardHistoryItemProcessor : GeneralItemProcessor<DataContract>
{
    protected override string ReportMessageName { get; set; } = "RewardHistoryContract";

    public RewardHistoryItemProcessor(
        ILogger<RewardHistoryItemProcessor> logger,
        IDataProducer<DataContract> dataProducer,
        IMetricsCollector metricsCollector) : base(logger, dataProducer, metricsCollector)
    {
        Name = nameof(RewardHistoryItemProcessor);
    }
}