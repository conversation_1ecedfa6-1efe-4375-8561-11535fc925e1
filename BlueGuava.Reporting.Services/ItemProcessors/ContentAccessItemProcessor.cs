using BlueGuava.EventStream.Common.Model.Classification.ContentAccess;
using BlueGuava.Extensions.AWS.EventStream.Producer;
using BlueGuava.Reporting.Common.Monitoring;
using Microsoft.Extensions.Logging;

namespace BlueGuava.Reporting.Services.ItemProcessors;

public class ContentAccessItemProcessor : GeneralItemProcessor<DataContract>
{
    protected override string ReportMessageName { get; set; } = "ContentAccessContract";

    public ContentAccessItemProcessor(
        ILogger<ContentAccessItemProcessor> logger,
        IDataProducer<DataContract> dataProducer,
        IMetricsCollector metricsCollector) : base(logger, dataProducer, metricsCollector)
    {
        Name = nameof(ContentAccessItemProcessor);
    }
}