using BlueGuava.Extensions.AsyncEnumerable;
using BlueGuava.Extensions.Logging;
using BlueGuava.Extensions.Paging;
using BlueGuava.Reporting.Common.Models;
using BlueGuava.Reporting.Common.Repositories;
using BlueGuava.Reporting.Common.Services;
using Microsoft.Extensions.Logging;

namespace BlueGuava.Reporting.Services;

public class ReportService<T> : IReportService<T> where T : ReportBase, new()
{
    private readonly ILogger<ReportService<T>> logger;
    private readonly IReportRepository<T> repository;

    public ReportService(ILogger<ReportService<T>> logger, IReportRepository<T> repository)
    {
        this.logger = logger;
        this.repository = repository;
    }

    public async Task<T> Create(T entity)
    {
        logger.LogDebug("Workflow: {Workflow}", entity.ToJson());

        return await repository.Create(entity);
    }

    public async Task<T> Update(T entity)
    {
        logger.LogDebug("Workflow: {Workflow}", entity.ToJson());

        await repository.Update(entity);

        return entity;
    }

    public System.Threading.Tasks.Task Delete(string id)
    {
        logger.LogDebug("Id: {Id}", id);

        return repository.Delete(id);
    }

    public async Task<T?> Retrieve(string id)
    {
        logger.LogDebug("Id: {Id}", id);

        return await repository.Retrieve(id);
    }

    public IAsyncEnumerable<T> Search(EntityFilter searchArgs, CancellationToken cancellationToken = default)
    {
        logger.LogDebug("SearchArgs: {@SearchArgs}", searchArgs);

        return repository.Search(searchArgs, true, cancellationToken);
    }

    public async Task<PagedResult<T>> StartPaging(EntityFilter searchArgs, int itemLimit, bool ascending)
    {
        logger.LogDebug("SearchArgs: {@SearchArgs}, ItemLimit: {ItemLimit}, Ascending: {Ascending}",
            searchArgs, itemLimit, ascending);

        return await repository.StartPaging(searchArgs, itemLimit, ascending);
    }

    public async Task<PagedResult<T>> GetNextPage(string pagingToken)
    {
        logger.LogDebug("PagingToken: {PagingToken}", pagingToken);

        return await repository.GetNextPage(pagingToken);
    }

    public async System.Threading.Tasks.Task InvalidateReports(string hashKey)
    {
        await repository.InvalidateReports(hashKey);
    }

    public async Task<List<T>> GetLastAvailableVersion(EntityFilter searchArgs)
    {
        logger.LogDebug("Get the latest available {Report} report", nameof(T));

        var search = new EntityFilter
        {
            ReportName = searchArgs.ReportName,
            EntityId = searchArgs.EntityId
        };

        var result = await repository.Search(search, false).FirstOrDefaultAsync();

        if (result is null)
        {
            return new List<T> { new() };
        }

        typeof(T).GetProperty("EntityId")?.SetValue(result, searchArgs.EntityId);

        return new List<T> { result };
    }
}